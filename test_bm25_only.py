"""
BM25-only evaluation script for full dataset testing
Tests BM25 retrieval with all queries and documents
"""
import os
import time
import json
import argparse
from config import Config
from src.modules.data_processor import DataProcessor
from src.modules.sparse_indexer import SparseIndexer
from src.modules.bm25_retriever import BM25Retriever
from src.modules.evaluator import IREvaluator

def print_separator(title: str, char="=", width=80):
    """Print a formatted separator"""
    print(f"\n{char * width}")
    print(f" {title}")
    print(f"{char * width}")

def print_progress(current: int, total: int, prefix: str = "Progress"):
    """Print progress bar"""
    percent = (current / total) * 100
    bar_length = 50
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    print(f'\r{prefix}: |{bar}| {percent:.1f}% ({current}/{total})', end='', flush=True)
    if current == total:
        print()

def main():
    """Main BM25 evaluation function"""
    parser = argparse.ArgumentParser(description='BM25-only IR Evaluation')
    parser.add_argument('--dataset', choices=['cranfield', 'vaswani', 'cacm', 'sample'], 
                       default='cranfield', help='Dataset to use')
    parser.add_argument('--top-k', type=int, default=100, help='Number of documents to retrieve')
    parser.add_argument('--sample-queries', type=int, help='Sample N queries for faster testing')
    parser.add_argument('--k1', type=float, default=1.2, help='BM25 k1 parameter')
    parser.add_argument('--b', type=float, default=0.75, help='BM25 b parameter')
    
    args = parser.parse_args()
    
    print_separator(f"BM25 EVALUATION - {args.dataset.upper()}")
    print(f"Dataset: {args.dataset}")
    print(f"Top-K Retrieval: {args.top_k}")
    print(f"BM25 Parameters: k1={args.k1}, b={args.b}")
    if args.sample_queries:
        print(f"Sample Queries: {args.sample_queries}")
    
    # Validate configuration
    try:
        Config.validate_config()
        print("✅ Configuration validated")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return
    
    # Initialize data processor
    print("\n📊 Loading dataset...")
    data_processor = DataProcessor()
    
    # Download/generate dataset
    if args.dataset == 'sample':
        success = data_processor.download_vaswani_dataset(use_real_data=False)
    else:
        success = data_processor.download_vaswani_dataset(use_real_data=True, dataset_name=args.dataset)
    
    if not success:
        print("❌ Failed to load dataset")
        return
    
    # Load dataset
    documents, queries, qrels = data_processor.load_dataset()
    
    # Sample queries if requested
    if args.sample_queries and args.sample_queries < len(queries):
        import random
        random.seed(42)
        queries = random.sample(queries, args.sample_queries)
        print(f"📝 Sampled {len(queries)} queries for testing")
    
    print(f"📈 Dataset loaded: {len(documents)} documents, {len(queries)} queries")
    
    # Build sparse index
    print("\n🏗️ Building sparse index...")
    sparse_indexer = SparseIndexer()
    start_time = time.time()
    sparse_indexer.build_index(documents)
    index_time = time.time() - start_time
    
    print(f"✅ Sparse index built in {index_time:.2f}s")
    print(f"   Vocabulary size: {sparse_indexer.get_vocabulary_size()}")
    print(f"   Average document length: {sparse_indexer.avg_doc_length:.2f}")
    print(f"   Total documents: {sparse_indexer.total_documents}")
    
    # Initialize BM25 retriever
    bm25_retriever = BM25Retriever(k1=args.k1, b=args.b)
    bm25_retriever.set_indexer(sparse_indexer)
    
    # Initialize evaluator
    evaluator = IREvaluator()
    
    print_separator("BM25 RETRIEVAL EVALUATION")
    
    # Perform retrieval for all queries
    print(f"🔍 Retrieving documents for {len(queries)} queries...")
    start_time = time.time()
    
    all_results = {}
    for i, query_info in enumerate(queries):
        query_id = query_info['query_id']
        query_text = query_info['query_text']
        
        # Retrieve documents
        retrieved_docs = bm25_retriever.retrieve(query_text, args.top_k)
        all_results[query_id] = retrieved_docs
        
        # Print progress every 10 queries or at the end
        if (i + 1) % 10 == 0 or i == len(queries) - 1:
            print_progress(i + 1, len(queries), "BM25 retrieval")
    
    retrieval_time = time.time() - start_time
    queries_per_second = len(queries) / retrieval_time
    
    print(f"\n✅ Retrieval completed in {retrieval_time:.2f}s")
    print(f"   Queries per second: {queries_per_second:.1f}")
    
    # Evaluate results
    print(f"\n📊 Computing evaluation metrics...")
    evaluation = evaluator.evaluate_batch(all_results, qrels)
    
    # Print results
    print_separator("EVALUATION RESULTS")
    
    print(f"\n📊 BM25 Performance Summary:")
    print(f"Dataset: {args.dataset} ({len(documents)} docs, {len(queries)} queries)")
    print(f"Parameters: k1={args.k1}, b={args.b}")
    print(f"Retrieval time: {retrieval_time:.2f}s ({queries_per_second:.1f} Q/s)")
    print(f"Index build time: {index_time:.2f}s")
    
    print(f"\n📈 Key Metrics:")
    metrics = evaluation['average_metrics']
    print(f"  MAP (Mean Average Precision): {metrics['MAP']:.4f}")
    print(f"  NDCG@10: {metrics['NDCG@10']:.4f}")
    print(f"  P@10 (Precision at 10): {metrics['P@10']:.4f}")
    print(f"  MRR (Mean Reciprocal Rank): {metrics['MRR']:.4f}")
    
    print(f"\n📋 Detailed Metrics:")
    for metric, value in metrics.items():
        print(f"  {metric}: {value:.4f}")
    
    # Query-level analysis
    print(f"\n🔍 Query-level Analysis:")
    query_results = evaluation['query_results']
    
    # Find best and worst performing queries
    query_aps = [(qr['query_id'], qr['metrics']['AP']) for qr in query_results]
    query_aps.sort(key=lambda x: x[1], reverse=True)
    
    print(f"  Best performing queries (by AP):")
    for i, (qid, ap) in enumerate(query_aps[:5]):
        query_text = next(q['query_text'] for q in queries if q['query_id'] == qid)
        print(f"    {i+1}. {qid} (AP={ap:.4f}): {query_text}")
    
    print(f"  Worst performing queries (by AP):")
    for i, (qid, ap) in enumerate(query_aps[-5:]):
        query_text = next(q['query_text'] for q in queries if q['query_id'] == qid)
        print(f"    {i+1}. {qid} (AP={ap:.4f}): {query_text}")
    
    # Performance distribution
    ap_values = [qr['metrics']['AP'] for qr in query_results]
    print(f"\n📊 Performance Distribution:")
    print(f"  Queries with AP > 0.8: {len([ap for ap in ap_values if ap > 0.8])}")
    print(f"  Queries with AP > 0.5: {len([ap for ap in ap_values if ap > 0.5])}")
    print(f"  Queries with AP > 0.2: {len([ap for ap in ap_values if ap > 0.2])}")
    print(f"  Queries with AP = 0.0: {len([ap for ap in ap_values if ap == 0.0])}")
    
    # Save results
    results_file = f"results/bm25_evaluation_{args.dataset}_{len(documents)}docs_{len(queries)}queries.json"
    
    # Prepare results for JSON serialization
    json_results = {
        'dataset': args.dataset,
        'num_documents': len(documents),
        'num_queries': len(queries),
        'parameters': {
            'top_k': args.top_k,
            'k1': args.k1,
            'b': args.b
        },
        'performance': {
            'retrieval_time': retrieval_time,
            'queries_per_second': queries_per_second,
            'index_build_time': index_time
        },
        'evaluation': {
            'average_metrics': evaluation['average_metrics'],
            'num_queries': evaluation['num_queries']
        },
        'index_statistics': {
            'vocabulary_size': sparse_indexer.get_vocabulary_size(),
            'avg_doc_length': sparse_indexer.avg_doc_length,
            'total_documents': sparse_indexer.total_documents
        }
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(json_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    print_separator("BM25 EVALUATION COMPLETE")
    print("🎉 BM25 evaluation completed successfully!")
    print(f"📊 Processed {len(queries)} queries against {len(documents)} documents")
    print(f"📈 Overall MAP: {metrics['MAP']:.4f}")
    
    # Performance summary
    if metrics['MAP'] > 0.4:
        print("🟢 Excellent retrieval performance!")
    elif metrics['MAP'] > 0.3:
        print("🟡 Good retrieval performance")
    elif metrics['MAP'] > 0.2:
        print("🟠 Moderate retrieval performance")
    else:
        print("🔴 Low retrieval performance - consider parameter tuning")

if __name__ == "__main__":
    main()
