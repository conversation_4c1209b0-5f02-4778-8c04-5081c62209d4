"""
BM25 parameter tuning script
Tests different k1 and b parameter combinations to find optimal settings
"""
import os
import time
import json
import itertools
from config import Config
from src.modules.data_processor import DataProcessor
from src.modules.sparse_indexer import SparseIndexer
from src.modules.bm25_retriever import BM25Retriever
from src.modules.evaluator import IREvaluator

def print_separator(title: str, char="=", width=80):
    """Print a formatted separator"""
    print(f"\n{char * width}")
    print(f" {title}")
    print(f"{char * width}")

def tune_bm25_parameters(documents, queries, qrels, sparse_indexer, evaluator, 
                        k1_values, b_values, top_k=100):
    """Tune BM25 parameters and return results"""
    
    results = []
    total_combinations = len(k1_values) * len(b_values)
    current = 0
    
    print(f"🔧 Testing {total_combinations} parameter combinations...")
    
    for k1, b in itertools.product(k1_values, b_values):
        current += 1
        print(f"\n[{current}/{total_combinations}] Testing k1={k1}, b={b}")
        
        # Initialize retriever with current parameters
        retriever = BM25Retriever(k1=k1, b=b)
        retriever.set_indexer(sparse_indexer)
        
        # Perform retrieval for all queries
        start_time = time.time()
        all_results = {}
        
        for query_info in queries:
            query_id = query_info['query_id']
            query_text = query_info['query_text']
            retrieved_docs = retriever.retrieve(query_text, top_k)
            all_results[query_id] = retrieved_docs
        
        retrieval_time = time.time() - start_time
        
        # Evaluate results
        evaluation = evaluator.evaluate_batch(all_results, qrels)
        
        # Store results
        result = {
            'k1': k1,
            'b': b,
            'map': evaluation['average_metrics']['MAP'],
            'ndcg_10': evaluation['average_metrics']['NDCG@10'],
            'p_10': evaluation['average_metrics']['P@10'],
            'mrr': evaluation['average_metrics']['MRR'],
            'retrieval_time': retrieval_time,
            'queries_per_second': len(queries) / retrieval_time
        }
        results.append(result)
        
        print(f"  MAP: {result['map']:.4f}, NDCG@10: {result['ndcg_10']:.4f}, P@10: {result['p_10']:.4f}")
    
    return results

def main():
    """Main parameter tuning function"""
    print_separator("BM25 PARAMETER TUNING")
    
    # Validate configuration
    try:
        Config.validate_config()
        print("✅ Configuration validated")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return
    
    # Load dataset
    print("\n📊 Loading dataset...")
    data_processor = DataProcessor()
    success = data_processor.download_vaswani_dataset(use_real_data=True, dataset_name='cranfield')
    
    if not success:
        print("❌ Failed to load dataset")
        return
    
    documents, queries, qrels = data_processor.load_dataset()
    print(f"📈 Dataset loaded: {len(documents)} documents, {len(queries)} queries")
    
    # Build sparse index
    print("\n🏗️ Building sparse index...")
    sparse_indexer = SparseIndexer()
    sparse_indexer.build_index(documents)
    print(f"✅ Index built with {sparse_indexer.get_vocabulary_size()} terms")
    
    # Initialize evaluator
    evaluator = IREvaluator()
    
    # Define parameter ranges to test
    k1_values = [0.8, 1.0, 1.2, 1.5, 1.8, 2.0]
    b_values = [0.0, 0.25, 0.5, 0.75, 1.0]
    
    print(f"\n🎯 Parameter ranges:")
    print(f"  k1: {k1_values}")
    print(f"  b: {b_values}")
    
    # Perform parameter tuning
    results = tune_bm25_parameters(
        documents, queries, qrels, sparse_indexer, evaluator,
        k1_values, b_values, top_k=100
    )
    
    # Analyze results
    print_separator("PARAMETER TUNING RESULTS")
    
    # Sort by MAP
    results_by_map = sorted(results, key=lambda x: x['map'], reverse=True)
    
    print(f"\n🏆 Top 10 parameter combinations (by MAP):")
    print(f"{'Rank':<4} {'k1':<4} {'b':<4} {'MAP':<8} {'NDCG@10':<8} {'P@10':<8} {'MRR':<8}")
    print("-" * 60)
    
    for i, result in enumerate(results_by_map[:10]):
        print(f"{i+1:<4} {result['k1']:<4} {result['b']:<4} "
              f"{result['map']:<8.4f} {result['ndcg_10']:<8.4f} "
              f"{result['p_10']:<8.4f} {result['mrr']:<8.4f}")
    
    # Best parameters
    best_result = results_by_map[0]
    print(f"\n🎯 Best parameters:")
    print(f"  k1 = {best_result['k1']}")
    print(f"  b = {best_result['b']}")
    print(f"  MAP = {best_result['map']:.4f}")
    print(f"  NDCG@10 = {best_result['ndcg_10']:.4f}")
    print(f"  P@10 = {best_result['p_10']:.4f}")
    
    # Performance analysis
    print(f"\n📊 Performance Analysis:")
    
    # Best k1 for each b value
    print(f"\nBest k1 for each b value:")
    for b in b_values:
        b_results = [r for r in results if r['b'] == b]
        best_b = max(b_results, key=lambda x: x['map'])
        print(f"  b={b}: k1={best_b['k1']} (MAP={best_b['map']:.4f})")
    
    # Best b for each k1 value
    print(f"\nBest b for each k1 value:")
    for k1 in k1_values:
        k1_results = [r for r in results if r['k1'] == k1]
        best_k1 = max(k1_results, key=lambda x: x['map'])
        print(f"  k1={k1}: b={best_k1['b']} (MAP={best_k1['map']:.4f})")
    
    # Parameter sensitivity analysis
    print(f"\n🔍 Parameter Sensitivity:")
    map_values = [r['map'] for r in results]
    map_range = max(map_values) - min(map_values)
    print(f"  MAP range: {min(map_values):.4f} - {max(map_values):.4f} (Δ={map_range:.4f})")
    
    if map_range > 0.05:
        print("  🔴 High sensitivity - parameter tuning is important")
    elif map_range > 0.02:
        print("  🟡 Moderate sensitivity - parameter tuning can help")
    else:
        print("  🟢 Low sensitivity - default parameters work well")
    
    # Save results
    results_file = "results/bm25_parameter_tuning_results.json"
    
    tuning_results = {
        'dataset': 'cranfield',
        'num_documents': len(documents),
        'num_queries': len(queries),
        'parameter_ranges': {
            'k1_values': k1_values,
            'b_values': b_values
        },
        'best_parameters': {
            'k1': best_result['k1'],
            'b': best_result['b'],
            'map': best_result['map'],
            'ndcg_10': best_result['ndcg_10'],
            'p_10': best_result['p_10'],
            'mrr': best_result['mrr']
        },
        'all_results': results,
        'performance_analysis': {
            'map_range': map_range,
            'best_map': max(map_values),
            'worst_map': min(map_values)
        }
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(tuning_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    print_separator("PARAMETER TUNING COMPLETE")
    print("🎉 BM25 parameter tuning completed successfully!")
    print(f"📊 Tested {len(results)} parameter combinations")
    print(f"🏆 Best MAP: {best_result['map']:.4f} (k1={best_result['k1']}, b={best_result['b']})")
    
    # Recommendation
    default_map = next(r['map'] for r in results if r['k1'] == 1.2 and r['b'] == 0.75)
    improvement = ((best_result['map'] - default_map) / default_map) * 100
    
    print(f"\n💡 Recommendation:")
    if improvement > 2:
        print(f"  Use optimized parameters (k1={best_result['k1']}, b={best_result['b']})")
        print(f"  Improvement over default: +{improvement:.1f}%")
    else:
        print(f"  Default parameters (k1=1.2, b=0.75) work well")
        print(f"  Potential improvement: +{improvement:.1f}%")

if __name__ == "__main__":
    main()
