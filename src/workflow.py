"""
LangGraph workflow for the Information Retrieval Experiment Platform
"""
from typing import Dict, <PERSON>, Tuple, TypedDict, Annotated
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages

from src.modules.data_processor import DataProcessor
from src.modules.sparse_indexer import SparseIndexer
from src.modules.dense_indexer import DenseIndexer
from src.modules.bm25_retriever import BM25Retriever
from src.modules.dense_retriever import DenseRetriever
from src.modules.llm_reranker import LLMReranker
from src.modules.evaluator import IREvaluator

class IRExperimentState(TypedDict):
    """State for the IR experiment workflow"""
    # Data
    documents: List[Dict]
    queries: List[Dict]
    qrels: Dict[str, Dict[str, int]]
    
    # Indices
    sparse_index_built: bool
    dense_index_built: bool
    
    # Retrieval results
    bm25_results: Dict[str, List[Tuple[str, float]]]
    dense_results: Dict[str, List[Tuple[str, float]]]
    reranked_results: Dict[str, List[Tuple[str, float]]]
    
    # Evaluation results
    bm25_evaluation: Dict
    dense_evaluation: Dict
    reranked_evaluation: Dict
    
    # Configuration
    top_k_retrieval: int
    top_k_rerank: int
    
    # Status and messages
    status: str
    messages: Annotated[List[str], add_messages]

class IRWorkflow:
    """Information Retrieval Experiment Workflow using LangGraph"""
    
    def __init__(self):
        self.data_processor = DataProcessor()
        self.sparse_indexer = SparseIndexer()
        self.dense_indexer = DenseIndexer()
        self.bm25_retriever = BM25Retriever()
        self.dense_retriever = DenseRetriever()
        self.llm_reranker = LLMReranker()
        self.evaluator = IREvaluator()
        
        # Build the workflow graph
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow"""
        
        workflow = StateGraph(IRExperimentState)
        
        # Add nodes
        workflow.add_node("load_data", self.load_data)
        workflow.add_node("build_sparse_index", self.build_sparse_index)
        workflow.add_node("build_dense_index", self.build_dense_index)
        workflow.add_node("bm25_retrieval", self.bm25_retrieval)
        workflow.add_node("dense_retrieval", self.dense_retrieval)
        workflow.add_node("llm_reranking", self.llm_reranking)
        workflow.add_node("evaluate_results", self.evaluate_results)
        
        # Define the flow
        workflow.set_entry_point("load_data")
        
        workflow.add_edge("load_data", "build_sparse_index")
        workflow.add_edge("build_sparse_index", "build_dense_index")
        workflow.add_edge("build_dense_index", "bm25_retrieval")
        workflow.add_edge("bm25_retrieval", "dense_retrieval")
        workflow.add_edge("dense_retrieval", "llm_reranking")
        workflow.add_edge("llm_reranking", "evaluate_results")
        workflow.add_edge("evaluate_results", END)
        
        return workflow.compile()
    
    def load_data(self, state: IRExperimentState) -> IRExperimentState:
        """Load dataset"""
        try:
            # Download/create dataset if not exists
            if not self.data_processor.download_vaswani_dataset():
                raise Exception("Failed to download dataset")
            
            # Load dataset
            documents, queries, qrels = self.data_processor.load_dataset()
            
            state["documents"] = documents
            state["queries"] = queries
            state["qrels"] = qrels
            state["status"] = "data_loaded"
            state["messages"] = [f"Loaded {len(documents)} documents, {len(queries)} queries"]
            
            return state
            
        except Exception as e:
            state["status"] = "error"
            state["messages"] = [f"Error loading data: {e}"]
            return state
    
    def build_sparse_index(self, state: IRExperimentState) -> IRExperimentState:
        """Build sparse index"""
        try:
            documents = state["documents"]
            
            # Build index
            success = self.sparse_indexer.build_index(documents)
            if not success:
                raise Exception("Failed to build sparse index")
            
            # Save index
            self.sparse_indexer.save_index()
            
            # Set up BM25 retriever
            self.bm25_retriever.set_indexer(self.sparse_indexer)
            
            state["sparse_index_built"] = True
            state["status"] = "sparse_index_built"
            state["messages"].append("Sparse index built successfully")
            
            return state
            
        except Exception as e:
            state["status"] = "error"
            state["messages"].append(f"Error building sparse index: {e}")
            return state
    
    def build_dense_index(self, state: IRExperimentState) -> IRExperimentState:
        """Build dense index"""
        try:
            documents = state["documents"]
            
            # Build index
            success = self.dense_indexer.build_index(documents)
            if not success:
                raise Exception("Failed to build dense index")
            
            # Save index
            self.dense_indexer.save_index()
            
            # Set up dense retriever
            self.dense_retriever.set_indexer(self.dense_indexer)
            
            state["dense_index_built"] = True
            state["status"] = "dense_index_built"
            state["messages"].append("Dense index built successfully")
            
            return state
            
        except Exception as e:
            state["status"] = "error"
            state["messages"].append(f"Error building dense index: {e}")
            return state
    
    def bm25_retrieval(self, state: IRExperimentState) -> IRExperimentState:
        """Perform BM25 retrieval"""
        try:
            queries = state["queries"]
            top_k = state.get("top_k_retrieval", 100)
            
            # Perform retrieval
            results = self.bm25_retriever.batch_retrieve(queries, top_k)
            
            state["bm25_results"] = results
            state["status"] = "bm25_retrieval_done"
            state["messages"].append(f"BM25 retrieval completed for {len(queries)} queries")
            
            return state
            
        except Exception as e:
            state["status"] = "error"
            state["messages"].append(f"Error in BM25 retrieval: {e}")
            return state
    
    def dense_retrieval(self, state: IRExperimentState) -> IRExperimentState:
        """Perform dense retrieval"""
        try:
            queries = state["queries"]
            top_k = state.get("top_k_retrieval", 100)
            
            # Perform retrieval
            results = self.dense_retriever.batch_retrieve(queries, top_k)
            
            state["dense_results"] = results
            state["status"] = "dense_retrieval_done"
            state["messages"].append(f"Dense retrieval completed for {len(queries)} queries")
            
            return state
            
        except Exception as e:
            state["status"] = "error"
            state["messages"].append(f"Error in dense retrieval: {e}")
            return state
    
    def llm_reranking(self, state: IRExperimentState) -> IRExperimentState:
        """Perform LLM reranking"""
        try:
            queries = state["queries"]
            bm25_results = state["bm25_results"]
            documents = state["documents"]
            top_k = state.get("top_k_rerank", 10)
            
            # Create document text mapping
            doc_texts = {doc['doc_id']: self.data_processor.get_document_text(doc) 
                        for doc in documents}
            
            # Perform reranking
            results = self.llm_reranker.batch_rerank(queries, bm25_results, doc_texts, top_k)
            
            state["reranked_results"] = results
            state["status"] = "reranking_done"
            state["messages"].append(f"LLM reranking completed for {len(queries)} queries")
            
            return state
            
        except Exception as e:
            state["status"] = "error"
            state["messages"].append(f"Error in LLM reranking: {e}")
            return state
    
    def evaluate_results(self, state: IRExperimentState) -> IRExperimentState:
        """Evaluate all results"""
        try:
            qrels = state["qrels"]
            
            # Evaluate BM25 results
            if "bm25_results" in state:
                bm25_eval = self.evaluator.evaluate_batch(state["bm25_results"], qrels)
                state["bm25_evaluation"] = bm25_eval
            
            # Evaluate dense results
            if "dense_results" in state:
                dense_eval = self.evaluator.evaluate_batch(state["dense_results"], qrels)
                state["dense_evaluation"] = dense_eval
            
            # Evaluate reranked results
            if "reranked_results" in state:
                reranked_eval = self.evaluator.evaluate_batch(state["reranked_results"], qrels)
                state["reranked_evaluation"] = reranked_eval
            
            state["status"] = "evaluation_complete"
            state["messages"].append("Evaluation completed for all systems")
            
            return state
            
        except Exception as e:
            state["status"] = "error"
            state["messages"].append(f"Error in evaluation: {e}")
            return state
    
    def run_experiment(self, top_k_retrieval: int = 100, top_k_rerank: int = 10) -> Dict:
        """Run the complete IR experiment"""
        
        initial_state = IRExperimentState(
            documents=[],
            queries=[],
            qrels={},
            sparse_index_built=False,
            dense_index_built=False,
            bm25_results={},
            dense_results={},
            reranked_results={},
            bm25_evaluation={},
            dense_evaluation={},
            reranked_evaluation={},
            top_k_retrieval=top_k_retrieval,
            top_k_rerank=top_k_rerank,
            status="initialized",
            messages=[]
        )
        
        # Run the workflow
        final_state = self.workflow.invoke(initial_state)
        
        return final_state
