"""
LLM-based reranking module using OpenRouter API
"""
import os
import json
import requests
from typing import List, Dict, Tuple
import time
from config import Config

class LLMReranker:
    """LLM-based reranker using list-wise ranking"""
    
    def __init__(self, model_name: str = None):
        self.config = Config()
        self.model_name = model_name or self.config.DEFAULT_MODEL
        self.api_key = self.config.OPENROUTER_API_KEY
        self.base_url = self.config.OPENROUTER_BASE_URL
        
        if not self.api_key:
            raise ValueError("OpenRouter API key not found in configuration")
    
    def _make_api_request(self, messages: List[Dict], max_retries: int = 3) -> str:
        """Make API request to OpenRouter"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/augment-code/ir-experiment-platform",
            "X-Title": "IR Experiment Platform"
        }
        
        data = {
            "model": self.model_name,
            "messages": messages,
            "temperature": 0.1,
            "max_tokens": 2000
        }
        
        # Set proxy if configured
        proxies = self.config.get_proxy_config()
        
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    proxies=proxies,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content']
                else:
                    print(f"API request failed with status {response.status_code}: {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                    
            except Exception as e:
                print(f"API request error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
        
        raise Exception("Failed to get response from LLM after multiple attempts")
    
    def _create_ranking_prompt(self, query: str, documents: List[Dict], doc_texts: Dict[str, str]) -> str:
        """Create prompt for list-wise ranking"""
        
        # Limit to top documents to avoid token limits
        max_docs = min(10, len(documents))
        top_documents = documents[:max_docs]
        
        prompt = f"""You are an expert information retrieval system. Your task is to rank documents by their relevance to a given query.

Query: "{query}"

Documents to rank:
"""
        
        for i, (doc_id, score) in enumerate(top_documents, 1):
            doc_text = doc_texts.get(doc_id, "")[:500]  # Truncate to avoid token limits
            prompt += f"\n[{i}] Document ID: {doc_id}\nContent: {doc_text}\n"
        
        prompt += f"""
Please rank these {len(top_documents)} documents from most relevant (1) to least relevant ({len(top_documents)}) based on how well they answer or relate to the query.

Provide your ranking as a JSON list of document IDs in order from most to least relevant. For example:
["doc_003", "doc_001", "doc_002"]

Consider:
- Semantic relevance to the query
- Coverage of query topics
- Quality and completeness of information

Ranking:"""
        
        return prompt
    
    def rerank(self, query: str, retrieved_docs: List[Tuple[str, float]], 
               doc_texts: Dict[str, str], top_k: int = 10) -> List[Tuple[str, float]]:
        """Rerank documents using LLM"""
        
        if not retrieved_docs:
            return []
        
        try:
            # Create ranking prompt
            prompt = self._create_ranking_prompt(query, retrieved_docs, doc_texts)
            
            messages = [
                {"role": "user", "content": prompt}
            ]
            
            # Get LLM response
            response = self._make_api_request(messages)
            
            # Parse ranking from response
            ranked_doc_ids = self._parse_ranking_response(response)
            
            # Create reranked results
            reranked_results = []
            original_scores = {doc_id: score for doc_id, score in retrieved_docs}
            
            # Add ranked documents with adjusted scores
            for i, doc_id in enumerate(ranked_doc_ids[:top_k]):
                if doc_id in original_scores:
                    # Use inverse rank as new score (higher rank = higher score)
                    new_score = 1.0 / (i + 1)
                    reranked_results.append((doc_id, new_score))
            
            # Add any remaining documents that weren't ranked
            ranked_set = set(ranked_doc_ids)
            for doc_id, original_score in retrieved_docs:
                if doc_id not in ranked_set and len(reranked_results) < top_k:
                    reranked_results.append((doc_id, original_score * 0.1))  # Lower score for unranked
            
            return reranked_results[:top_k]
            
        except Exception as e:
            print(f"Error during reranking: {e}")
            # Fallback to original ranking
            return retrieved_docs[:top_k]
    
    def _parse_ranking_response(self, response: str) -> List[str]:
        """Parse ranking from LLM response"""
        try:
            # Try to find JSON in the response
            import re
            
            # Look for JSON array pattern
            json_match = re.search(r'\[([^\]]+)\]', response)
            if json_match:
                json_str = json_match.group(0)
                # Clean up the JSON string
                json_str = re.sub(r'["""]', '"', json_str)  # Normalize quotes
                ranked_ids = json.loads(json_str)
                return ranked_ids
            
            # Fallback: look for document IDs in order
            doc_ids = re.findall(r'doc_\d+', response)
            return doc_ids
            
        except Exception as e:
            print(f"Error parsing ranking response: {e}")
            print(f"Response: {response}")
            return []
    
    def batch_rerank(self, queries: List[Dict], retrieval_results: Dict[str, List[Tuple[str, float]]], 
                     doc_texts: Dict[str, str], top_k: int = 10) -> Dict[str, List[Tuple[str, float]]]:
        """Rerank results for multiple queries"""
        reranked_results = {}
        
        for query_info in queries:
            query_id = query_info['query_id']
            query_text = query_info['query_text']
            
            if query_id in retrieval_results:
                retrieved_docs = retrieval_results[query_id]
                reranked_results[query_id] = self.rerank(query_text, retrieved_docs, doc_texts, top_k)
            else:
                reranked_results[query_id] = []
        
        return reranked_results
