"""
BM25 retrieval module for sparse retrieval
"""
import math
from typing import List, Dict, Tuple
from collections import Counter
from src.modules.sparse_indexer import SparseIndexer

class BM25Retriever:
    """BM25 retriever for sparse retrieval"""
    
    def __init__(self, k1: float = 1.2, b: float = 0.75):
        self.k1 = k1  # Term frequency saturation parameter
        self.b = b    # Length normalization parameter
        self.indexer = None
        
    def set_indexer(self, indexer: SparseIndexer):
        """Set the sparse indexer"""
        self.indexer = indexer
    
    def tokenize(self, text: str) -> List[str]:
        """Tokenize query text (should match indexer's tokenization)"""
        import re
        tokens = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        return tokens
    
    def calculate_bm25_score(self, query_terms: List[str], doc_id: str) -> float:
        """Calculate BM25 score for a document given query terms"""
        if not self.indexer:
            raise ValueError("Indexer not set. Please set indexer first.")
        
        score = 0.0
        doc_length = self.indexer.get_document_length(doc_id)
        avg_doc_length = self.indexer.avg_doc_length
        total_docs = self.indexer.total_documents
        
        # Count query term frequencies
        query_term_counts = Counter(query_terms)
        
        for term, query_tf in query_term_counts.items():
            # Get term frequency in document
            doc_tf = self.indexer.get_term_frequency(term, doc_id)
            
            if doc_tf == 0:
                continue
            
            # Get document frequency
            doc_freq = self.indexer.get_document_frequency(term)
            
            if doc_freq == 0:
                continue
            
            # Calculate IDF
            idf = math.log((total_docs - doc_freq + 0.5) / (doc_freq + 0.5))
            
            # Calculate TF component
            tf_component = (doc_tf * (self.k1 + 1)) / (
                doc_tf + self.k1 * (1 - self.b + self.b * (doc_length / avg_doc_length))
            )
            
            # Add to score
            score += idf * tf_component * query_tf
        
        return score
    
    def retrieve(self, query: str, top_k: int = 100) -> List[Tuple[str, float]]:
        """Retrieve top-k documents for a query using BM25"""
        if not self.indexer:
            raise ValueError("Indexer not set. Please set indexer first.")
        
        # Tokenize query
        query_terms = self.tokenize(query)
        
        if not query_terms:
            return []
        
        # Get candidate documents (union of posting lists)
        candidate_docs = set()
        for term in query_terms:
            posting_list = self.indexer.get_posting_list(term)
            candidate_docs.update(posting_list.keys())
        
        if not candidate_docs:
            return []
        
        # Calculate BM25 scores for candidate documents
        doc_scores = []
        for doc_id in candidate_docs:
            score = self.calculate_bm25_score(query_terms, doc_id)
            doc_scores.append((doc_id, score))
        
        # Sort by score (descending) and return top-k
        doc_scores.sort(key=lambda x: x[1], reverse=True)
        return doc_scores[:top_k]
    
    def batch_retrieve(self, queries: List[Dict], top_k: int = 100) -> Dict[str, List[Tuple[str, float]]]:
        """Retrieve for multiple queries"""
        results = {}
        
        for query_info in queries:
            query_id = query_info['query_id']
            query_text = query_info['query_text']
            
            results[query_id] = self.retrieve(query_text, top_k)
        
        return results
    
    def explain_score(self, query: str, doc_id: str) -> Dict:
        """Explain BM25 score calculation for debugging"""
        if not self.indexer:
            raise ValueError("Indexer not set. Please set indexer first.")
        
        query_terms = self.tokenize(query)
        doc_length = self.indexer.get_document_length(doc_id)
        avg_doc_length = self.indexer.avg_doc_length
        total_docs = self.indexer.total_documents
        
        explanation = {
            'query_terms': query_terms,
            'doc_id': doc_id,
            'doc_length': doc_length,
            'avg_doc_length': avg_doc_length,
            'total_docs': total_docs,
            'k1': self.k1,
            'b': self.b,
            'term_scores': {},
            'total_score': 0.0
        }
        
        query_term_counts = Counter(query_terms)
        total_score = 0.0
        
        for term, query_tf in query_term_counts.items():
            doc_tf = self.indexer.get_term_frequency(term, doc_id)
            doc_freq = self.indexer.get_document_frequency(term)
            
            if doc_tf == 0 or doc_freq == 0:
                explanation['term_scores'][term] = {
                    'doc_tf': doc_tf,
                    'doc_freq': doc_freq,
                    'idf': 0.0,
                    'tf_component': 0.0,
                    'score': 0.0
                }
                continue
            
            # Calculate components
            idf = math.log((total_docs - doc_freq + 0.5) / (doc_freq + 0.5))
            tf_component = (doc_tf * (self.k1 + 1)) / (
                doc_tf + self.k1 * (1 - self.b + self.b * (doc_length / avg_doc_length))
            )
            term_score = idf * tf_component * query_tf
            total_score += term_score
            
            explanation['term_scores'][term] = {
                'query_tf': query_tf,
                'doc_tf': doc_tf,
                'doc_freq': doc_freq,
                'idf': idf,
                'tf_component': tf_component,
                'score': term_score
            }
        
        explanation['total_score'] = total_score
        return explanation
