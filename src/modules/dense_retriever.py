"""
Dense retrieval module using sentence-transformers
"""
from typing import List, Dict, Tuple
from src.modules.dense_indexer import DenseIndexer

class DenseRetriever:
    """Dense retriever using semantic embeddings"""
    
    def __init__(self, model_name: str = None):
        self.indexer = DenseIndexer(model_name)
        
    def set_indexer(self, indexer: DenseIndexer):
        """Set the dense indexer"""
        self.indexer = indexer
    
    def retrieve(self, query: str, top_k: int = 100) -> List[Tuple[str, float]]:
        """Retrieve top-k documents for a query using dense retrieval"""
        return self.indexer.search(query, top_k)
    
    def batch_retrieve(self, queries: List[Dict], top_k: int = 100) -> Dict[str, List[Tuple[str, float]]]:
        """Retrieve for multiple queries"""
        results = {}
        
        for query_info in queries:
            query_id = query_info['query_id']
            query_text = query_info['query_text']
            
            results[query_id] = self.retrieve(query_text, top_k)
        
        return results
    
    def get_query_embedding(self, query: str):
        """Get embedding for a query"""
        if self.indexer.model is None:
            self.indexer._load_model()
        
        return self.indexer.model.encode([query], convert_to_numpy=True)[0]
    
    def calculate_similarity(self, query: str, doc_id: str) -> float:
        """Calculate similarity between query and document"""
        import numpy as np
        
        # Get query embedding
        query_embedding = self.get_query_embedding(query)
        
        # Get document embedding
        doc_embedding = self.indexer.get_document_embedding(doc_id)
        
        if doc_embedding is None:
            return 0.0
        
        # Calculate cosine similarity
        query_norm = np.linalg.norm(query_embedding)
        doc_norm = np.linalg.norm(doc_embedding)
        
        if query_norm == 0 or doc_norm == 0:
            return 0.0
        
        similarity = np.dot(query_embedding, doc_embedding) / (query_norm * doc_norm)
        return float(similarity)
