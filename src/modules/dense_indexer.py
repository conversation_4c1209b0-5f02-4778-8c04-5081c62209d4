"""
Dense indexing module using sentence-transformers for semantic embeddings
"""
import os
import pickle
import numpy as np
from typing import List, Dict, Tuple
import faiss
from sentence_transformers import SentenceTransformer
from tqdm import tqdm
from config import Config

class DenseIndexer:
    """Dense indexer using sentence transformers for semantic embeddings"""
    
    def __init__(self, model_name: str = None):
        self.config = Config()
        self.model_name = model_name or self.config.EMBEDDING_MODEL
        self.model = None
        self.index = None
        self.doc_embeddings = None
        self.doc_ids = []
        self.embedding_dim = None
        
    def _load_model(self):
        """Load the sentence transformer model"""
        if self.model is None:
            print(f"Loading embedding model: {self.model_name}")
            try:
                # Set proxy if configured
                proxy_config = self.config.get_proxy_config()
                if proxy_config:
                    os.environ['HTTP_PROXY'] = proxy_config.get('http', '')
                    os.environ['HTTPS_PROXY'] = proxy_config.get('https', '')
                
                self.model = SentenceTransformer(self.model_name)
                self.embedding_dim = self.model.get_sentence_embedding_dimension()
                print(f"Model loaded successfully. Embedding dimension: {self.embedding_dim}")
                
            except Exception as e:
                print(f"Error loading model: {e}")
                raise
    
    def build_index(self, documents: List[Dict]) -> bool:
        """Build dense index from documents"""
        try:
            self._load_model()
            
            print("Building dense index...")
            
            # Extract document texts and IDs
            doc_texts = []
            self.doc_ids = []
            
            for doc in documents:
                doc_id = doc['doc_id']
                text = self._get_document_text(doc)
                doc_texts.append(text)
                self.doc_ids.append(doc_id)
            
            # Generate embeddings in batches
            print("Generating embeddings...")
            batch_size = self.config.BATCH_SIZE
            embeddings = []
            
            for i in tqdm(range(0, len(doc_texts), batch_size)):
                batch_texts = doc_texts[i:i + batch_size]
                batch_embeddings = self.model.encode(
                    batch_texts,
                    convert_to_numpy=True,
                    show_progress_bar=False
                )
                embeddings.append(batch_embeddings)
            
            # Concatenate all embeddings
            self.doc_embeddings = np.vstack(embeddings)
            
            # Build FAISS index
            print("Building FAISS index...")
            self.index = faiss.IndexFlatIP(self.embedding_dim)  # Inner product for cosine similarity
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(self.doc_embeddings)
            self.index.add(self.doc_embeddings)
            
            print(f"Dense index built successfully!")
            print(f"Number of documents: {len(self.doc_ids)}")
            print(f"Embedding dimension: {self.embedding_dim}")
            
            return True
            
        except Exception as e:
            print(f"Error building dense index: {e}")
            return False
    
    def _get_document_text(self, doc: Dict) -> str:
        """Extract text from document"""
        title = doc.get('title', '')
        content = doc.get('content', '')
        return f"{title} {content}".strip()
    
    def save_index(self, filename: str = "dense_index"):
        """Save dense index to files"""
        try:
            index_dir = self.config.INDEX_DIR
            
            # Save FAISS index
            faiss_path = os.path.join(index_dir, f"{filename}.faiss")
            faiss.write_index(self.index, faiss_path)
            
            # Save metadata
            metadata_path = os.path.join(index_dir, f"{filename}_metadata.pkl")
            metadata = {
                'doc_ids': self.doc_ids,
                'embedding_dim': self.embedding_dim,
                'model_name': self.model_name,
                'doc_embeddings': self.doc_embeddings
            }
            
            with open(metadata_path, 'wb') as f:
                pickle.dump(metadata, f)
            
            print(f"Dense index saved to {faiss_path} and {metadata_path}")
            return True
            
        except Exception as e:
            print(f"Error saving dense index: {e}")
            return False
    
    def load_index(self, filename: str = "dense_index"):
        """Load dense index from files"""
        try:
            index_dir = self.config.INDEX_DIR
            
            # Load FAISS index
            faiss_path = os.path.join(index_dir, f"{filename}.faiss")
            self.index = faiss.read_index(faiss_path)
            
            # Load metadata
            metadata_path = os.path.join(index_dir, f"{filename}_metadata.pkl")
            with open(metadata_path, 'rb') as f:
                metadata = pickle.load(f)
            
            self.doc_ids = metadata['doc_ids']
            self.embedding_dim = metadata['embedding_dim']
            self.model_name = metadata['model_name']
            self.doc_embeddings = metadata['doc_embeddings']
            
            # Load model
            self._load_model()
            
            print(f"Dense index loaded from {faiss_path}")
            return True
            
        except FileNotFoundError:
            print(f"Dense index files not found")
            return False
        except Exception as e:
            print(f"Error loading dense index: {e}")
            return False
    
    def search(self, query: str, top_k: int = 100) -> List[Tuple[str, float]]:
        """Search for similar documents using dense retrieval"""
        try:
            if self.model is None or self.index is None:
                raise ValueError("Index not loaded. Please build or load index first.")
            
            # Encode query
            query_embedding = self.model.encode([query], convert_to_numpy=True)
            
            # Normalize for cosine similarity
            faiss.normalize_L2(query_embedding)
            
            # Search
            scores, indices = self.index.search(query_embedding, top_k)
            
            # Format results
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx != -1:  # Valid result
                    doc_id = self.doc_ids[idx]
                    results.append((doc_id, float(score)))
            
            return results
            
        except Exception as e:
            print(f"Error during dense search: {e}")
            return []
    
    def get_document_embedding(self, doc_id: str) -> np.ndarray:
        """Get embedding for a specific document"""
        try:
            doc_idx = self.doc_ids.index(doc_id)
            return self.doc_embeddings[doc_idx]
        except ValueError:
            return None
    
    def get_index_statistics(self) -> Dict:
        """Get index statistics"""
        return {
            'num_documents': len(self.doc_ids),
            'embedding_dimension': self.embedding_dim,
            'model_name': self.model_name,
            'index_type': 'FAISS IndexFlatIP'
        }
