"""
Evaluation module for information retrieval metrics
"""
import math
from typing import List, Dict, Tuple
import numpy as np

class IREvaluator:
    """Information Retrieval Evaluator with standard metrics"""
    
    def __init__(self):
        pass
    
    def precision_at_k(self, retrieved_docs: List[str], relevant_docs: List[str], k: int) -> float:
        """Calculate Precision@K"""
        if k <= 0 or not retrieved_docs:
            return 0.0
        
        retrieved_at_k = retrieved_docs[:k]
        relevant_retrieved = len([doc for doc in retrieved_at_k if doc in relevant_docs])
        
        return relevant_retrieved / min(k, len(retrieved_at_k))
    
    def recall_at_k(self, retrieved_docs: List[str], relevant_docs: List[str], k: int) -> float:
        """Calculate Recall@K"""
        if not relevant_docs or k <= 0:
            return 0.0
        
        retrieved_at_k = retrieved_docs[:k]
        relevant_retrieved = len([doc for doc in retrieved_at_k if doc in relevant_docs])
        
        return relevant_retrieved / len(relevant_docs)
    
    def f1_at_k(self, retrieved_docs: List[str], relevant_docs: List[str], k: int) -> float:
        """Calculate F1@K"""
        precision = self.precision_at_k(retrieved_docs, relevant_docs, k)
        recall = self.recall_at_k(retrieved_docs, relevant_docs, k)
        
        if precision + recall == 0:
            return 0.0
        
        return 2 * (precision * recall) / (precision + recall)
    
    def average_precision(self, retrieved_docs: List[str], relevant_docs: List[str]) -> float:
        """Calculate Average Precision (AP)"""
        if not relevant_docs or not retrieved_docs:
            return 0.0
        
        relevant_set = set(relevant_docs)
        precision_sum = 0.0
        relevant_count = 0
        
        for i, doc in enumerate(retrieved_docs):
            if doc in relevant_set:
                relevant_count += 1
                precision_at_i = relevant_count / (i + 1)
                precision_sum += precision_at_i
        
        if relevant_count == 0:
            return 0.0
        
        return precision_sum / len(relevant_docs)
    
    def dcg_at_k(self, retrieved_docs: List[str], relevance_scores: Dict[str, int], k: int) -> float:
        """Calculate Discounted Cumulative Gain at K"""
        if k <= 0 or not retrieved_docs:
            return 0.0
        
        dcg = 0.0
        for i, doc in enumerate(retrieved_docs[:k]):
            relevance = relevance_scores.get(doc, 0)
            if i == 0:
                dcg += relevance
            else:
                dcg += relevance / math.log2(i + 1)
        
        return dcg
    
    def ndcg_at_k(self, retrieved_docs: List[str], relevance_scores: Dict[str, int], k: int) -> float:
        """Calculate Normalized Discounted Cumulative Gain at K"""
        dcg = self.dcg_at_k(retrieved_docs, relevance_scores, k)
        
        # Calculate ideal DCG
        ideal_docs = sorted(relevance_scores.keys(), key=lambda x: relevance_scores[x], reverse=True)
        idcg = self.dcg_at_k(ideal_docs, relevance_scores, k)
        
        if idcg == 0:
            return 0.0
        
        return dcg / idcg
    
    def mean_reciprocal_rank(self, retrieved_docs: List[str], relevant_docs: List[str]) -> float:
        """Calculate Mean Reciprocal Rank (for single query)"""
        if not relevant_docs or not retrieved_docs:
            return 0.0
        
        relevant_set = set(relevant_docs)
        
        for i, doc in enumerate(retrieved_docs):
            if doc in relevant_set:
                return 1.0 / (i + 1)
        
        return 0.0
    
    def evaluate_query(self, query_id: str, retrieved_docs: List[Tuple[str, float]], 
                      qrels: Dict[str, int], k_values: List[int] = [1, 5, 10, 20]) -> Dict:
        """Evaluate a single query with multiple metrics"""
        
        # Extract document IDs from retrieved results
        retrieved_doc_ids = [doc_id for doc_id, score in retrieved_docs]
        
        # Get relevant documents
        relevant_docs = [doc_id for doc_id, rel in qrels.items() if rel > 0]
        
        results = {
            'query_id': query_id,
            'num_retrieved': len(retrieved_doc_ids),
            'num_relevant': len(relevant_docs),
            'metrics': {}
        }
        
        # Calculate metrics for different k values
        for k in k_values:
            results['metrics'][f'P@{k}'] = self.precision_at_k(retrieved_doc_ids, relevant_docs, k)
            results['metrics'][f'R@{k}'] = self.recall_at_k(retrieved_doc_ids, relevant_docs, k)
            results['metrics'][f'F1@{k}'] = self.f1_at_k(retrieved_doc_ids, relevant_docs, k)
            results['metrics'][f'NDCG@{k}'] = self.ndcg_at_k(retrieved_doc_ids, qrels, k)
        
        # Calculate other metrics
        results['metrics']['AP'] = self.average_precision(retrieved_doc_ids, relevant_docs)
        results['metrics']['MRR'] = self.mean_reciprocal_rank(retrieved_doc_ids, relevant_docs)
        
        return results
    
    def evaluate_batch(self, retrieval_results: Dict[str, List[Tuple[str, float]]], 
                      qrels: Dict[str, Dict[str, int]], 
                      k_values: List[int] = [1, 5, 10, 20]) -> Dict:
        """Evaluate multiple queries and compute average metrics"""
        
        query_results = []
        
        # Evaluate each query
        for query_id, retrieved_docs in retrieval_results.items():
            if query_id in qrels:
                query_qrels = qrels[query_id]
                query_result = self.evaluate_query(query_id, retrieved_docs, query_qrels, k_values)
                query_results.append(query_result)
        
        if not query_results:
            return {'error': 'No queries to evaluate'}
        
        # Calculate average metrics
        avg_metrics = {}
        metric_names = query_results[0]['metrics'].keys()
        
        for metric in metric_names:
            values = [result['metrics'][metric] for result in query_results]
            avg_metrics[metric] = np.mean(values)
        
        # Calculate MAP (Mean Average Precision)
        ap_values = [result['metrics']['AP'] for result in query_results]
        avg_metrics['MAP'] = np.mean(ap_values)
        
        return {
            'num_queries': len(query_results),
            'average_metrics': avg_metrics,
            'query_results': query_results
        }
    
    def compare_systems(self, system1_results: Dict, system2_results: Dict, 
                       system1_name: str = "System 1", system2_name: str = "System 2") -> Dict:
        """Compare two retrieval systems"""
        
        comparison = {
            'system1_name': system1_name,
            'system2_name': system2_name,
            'improvements': {},
            'summary': {}
        }
        
        metrics1 = system1_results['average_metrics']
        metrics2 = system2_results['average_metrics']
        
        for metric in metrics1.keys():
            if metric in metrics2:
                value1 = metrics1[metric]
                value2 = metrics2[metric]
                
                improvement = ((value2 - value1) / value1 * 100) if value1 > 0 else 0
                comparison['improvements'][metric] = {
                    system1_name: value1,
                    system2_name: value2,
                    'improvement_pct': improvement
                }
        
        # Summary statistics
        improvements = [comp['improvement_pct'] for comp in comparison['improvements'].values()]
        comparison['summary'] = {
            'avg_improvement': np.mean(improvements),
            'num_improved_metrics': len([imp for imp in improvements if imp > 0]),
            'num_degraded_metrics': len([imp for imp in improvements if imp < 0])
        }
        
        return comparison
