"""
Sparse indexing module for building inverted indices
"""
import os
import json
import pickle
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from config import Config

class SparseIndexer:
    """Sparse indexer for building inverted indices"""
    
    def __init__(self):
        self.config = Config()
        self.inverted_index = defaultdict(dict)  # term -> {doc_id: tf}
        self.document_frequencies = defaultdict(int)  # term -> df
        self.document_lengths = {}  # doc_id -> length
        self.vocabulary = set()
        self.total_documents = 0
        self.avg_doc_length = 0
        
    def tokenize(self, text: str) -> List[str]:
        """Simple tokenization"""
        # Convert to lowercase and split on non-alphanumeric characters
        tokens = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        return tokens
    
    def build_index(self, documents: List[Dict]) -> bool:
        """Build inverted index from documents"""
        try:
            print("Building sparse index...")
            self.total_documents = len(documents)
            
            # First pass: collect term frequencies and document lengths
            for doc in documents:
                doc_id = doc['doc_id']
                text = self._get_document_text(doc)
                tokens = self.tokenize(text)
                
                # Calculate term frequencies for this document
                term_counts = Counter(tokens)
                self.document_lengths[doc_id] = len(tokens)
                
                # Update inverted index
                for term, tf in term_counts.items():
                    self.inverted_index[term][doc_id] = tf
                    self.vocabulary.add(term)
            
            # Second pass: calculate document frequencies
            for term in self.vocabulary:
                self.document_frequencies[term] = len(self.inverted_index[term])
            
            # Calculate average document length
            self.avg_doc_length = sum(self.document_lengths.values()) / len(self.document_lengths)
            
            print(f"Index built successfully!")
            print(f"Vocabulary size: {len(self.vocabulary)}")
            print(f"Total documents: {self.total_documents}")
            print(f"Average document length: {self.avg_doc_length:.2f}")
            
            return True
            
        except Exception as e:
            print(f"Error building index: {e}")
            return False
    
    def _get_document_text(self, doc: Dict) -> str:
        """Extract text from document"""
        title = doc.get('title', '')
        content = doc.get('content', '')
        return f"{title} {content}".strip()
    
    def save_index(self, filename: str = "sparse_index.pkl"):
        """Save index to file"""
        try:
            index_path = os.path.join(self.config.INDEX_DIR, filename)
            index_data = {
                'inverted_index': dict(self.inverted_index),
                'document_frequencies': dict(self.document_frequencies),
                'document_lengths': self.document_lengths,
                'vocabulary': self.vocabulary,
                'total_documents': self.total_documents,
                'avg_doc_length': self.avg_doc_length
            }
            
            with open(index_path, 'wb') as f:
                pickle.dump(index_data, f)
            
            print(f"Index saved to {index_path}")
            return True
            
        except Exception as e:
            print(f"Error saving index: {e}")
            return False
    
    def load_index(self, filename: str = "sparse_index.pkl"):
        """Load index from file"""
        try:
            index_path = os.path.join(self.config.INDEX_DIR, filename)
            
            with open(index_path, 'rb') as f:
                index_data = pickle.load(f)
            
            self.inverted_index = defaultdict(dict, index_data['inverted_index'])
            self.document_frequencies = defaultdict(int, index_data['document_frequencies'])
            self.document_lengths = index_data['document_lengths']
            self.vocabulary = index_data['vocabulary']
            self.total_documents = index_data['total_documents']
            self.avg_doc_length = index_data['avg_doc_length']
            
            print(f"Index loaded from {index_path}")
            return True
            
        except FileNotFoundError:
            print(f"Index file not found: {index_path}")
            return False
        except Exception as e:
            print(f"Error loading index: {e}")
            return False
    
    def get_term_frequency(self, term: str, doc_id: str) -> int:
        """Get term frequency for a term in a document"""
        return self.inverted_index.get(term, {}).get(doc_id, 0)
    
    def get_document_frequency(self, term: str) -> int:
        """Get document frequency for a term"""
        return self.document_frequencies.get(term, 0)
    
    def get_posting_list(self, term: str) -> Dict[str, int]:
        """Get posting list for a term"""
        return self.inverted_index.get(term, {})
    
    def get_document_length(self, doc_id: str) -> int:
        """Get document length"""
        return self.document_lengths.get(doc_id, 0)
    
    def get_vocabulary_size(self) -> int:
        """Get vocabulary size"""
        return len(self.vocabulary)
    
    def get_index_statistics(self) -> Dict:
        """Get index statistics"""
        return {
            'vocabulary_size': len(self.vocabulary),
            'total_documents': self.total_documents,
            'avg_doc_length': self.avg_doc_length,
            'total_postings': sum(len(postings) for postings in self.inverted_index.values())
        }
