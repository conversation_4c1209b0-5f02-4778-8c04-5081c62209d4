"""
Data processing module for handling Vaswani dataset and other IR datasets
"""
import os
import json
import pandas as pd
from typing import List, Dict, Tuple, Optional
import requests
from tqdm import tqdm
import xml.etree.ElementTree as ET
from config import Config

class DataProcessor:
    """Data processor for information retrieval datasets"""
    
    def __init__(self):
        self.config = Config()
        self.documents = []
        self.queries = []
        self.qrels = {}  # Query relevance judgments
    
    def download_vaswani_dataset(self) -> bool:
        """
        Download Vaswani dataset (a classic IR test collection)
        Note: This is a placeholder - you may need to adapt based on actual data source
        """
        try:
            # Create sample Vaswani-like dataset for demonstration
            self._create_sample_dataset()
            return True
        except Exception as e:
            print(f"Error downloading dataset: {e}")
            return False
    
    def _create_sample_dataset(self):
        """Create a sample dataset for demonstration purposes"""
        
        # Sample documents (simulating Vaswani collection)
        sample_docs = [
            {
                "doc_id": "doc_001",
                "title": "Information Retrieval Systems",
                "content": "Information retrieval systems are designed to help users find relevant documents from large collections. These systems use various techniques including indexing, ranking, and relevance feedback."
            },
            {
                "doc_id": "doc_002", 
                "title": "Vector Space Model",
                "content": "The vector space model represents documents and queries as vectors in a high-dimensional space. Similarity between documents and queries is computed using cosine similarity or other distance metrics."
            },
            {
                "doc_id": "doc_003",
                "title": "Boolean Retrieval",
                "content": "Boolean retrieval systems use Boolean logic to match documents with queries. Users can combine terms using AND, OR, and NOT operators to specify their information needs."
            },
            {
                "doc_id": "doc_004",
                "title": "Probabilistic Models",
                "content": "Probabilistic models in information retrieval estimate the probability that a document is relevant to a query. The BM25 algorithm is a popular probabilistic ranking function."
            },
            {
                "doc_id": "doc_005",
                "title": "Neural Information Retrieval",
                "content": "Neural approaches to information retrieval use deep learning models to learn representations of documents and queries. These models can capture semantic similarities beyond exact term matching."
            }
        ]
        
        # Sample queries
        sample_queries = [
            {
                "query_id": "q001",
                "query_text": "information retrieval systems"
            },
            {
                "query_id": "q002", 
                "query_text": "vector space model similarity"
            },
            {
                "query_id": "q003",
                "query_text": "boolean logic retrieval"
            }
        ]
        
        # Sample relevance judgments (qrels)
        sample_qrels = {
            "q001": {"doc_001": 2, "doc_002": 1, "doc_005": 1},  # 2=highly relevant, 1=relevant, 0=not relevant
            "q002": {"doc_002": 2, "doc_001": 1},
            "q003": {"doc_003": 2, "doc_001": 1}
        }
        
        # Save to files
        self._save_documents(sample_docs)
        self._save_queries(sample_queries)
        self._save_qrels(sample_qrels)
        
        # Load into memory
        self.documents = sample_docs
        self.queries = sample_queries
        self.qrels = sample_qrels
    
    def _save_documents(self, documents: List[Dict]):
        """Save documents to file"""
        doc_path = os.path.join(self.config.DATA_DIR, "documents.json")
        with open(doc_path, 'w', encoding='utf-8') as f:
            json.dump(documents, f, indent=2, ensure_ascii=False)
    
    def _save_queries(self, queries: List[Dict]):
        """Save queries to file"""
        query_path = os.path.join(self.config.DATA_DIR, "queries.json")
        with open(query_path, 'w', encoding='utf-8') as f:
            json.dump(queries, f, indent=2, ensure_ascii=False)
    
    def _save_qrels(self, qrels: Dict):
        """Save relevance judgments to file"""
        qrels_path = os.path.join(self.config.DATA_DIR, "qrels.json")
        with open(qrels_path, 'w', encoding='utf-8') as f:
            json.dump(qrels, f, indent=2, ensure_ascii=False)
    
    def load_dataset(self) -> Tuple[List[Dict], List[Dict], Dict]:
        """Load dataset from files"""
        try:
            # Load documents
            doc_path = os.path.join(self.config.DATA_DIR, "documents.json")
            with open(doc_path, 'r', encoding='utf-8') as f:
                self.documents = json.load(f)
            
            # Load queries
            query_path = os.path.join(self.config.DATA_DIR, "queries.json")
            with open(query_path, 'r', encoding='utf-8') as f:
                self.queries = json.load(f)
            
            # Load qrels
            qrels_path = os.path.join(self.config.DATA_DIR, "qrels.json")
            with open(qrels_path, 'r', encoding='utf-8') as f:
                self.qrels = json.load(f)
            
            return self.documents, self.queries, self.qrels
            
        except FileNotFoundError:
            print("Dataset files not found. Please download the dataset first.")
            return [], [], {}
    
    def get_document_text(self, doc: Dict) -> str:
        """Extract full text from document"""
        title = doc.get('title', '')
        content = doc.get('content', '')
        return f"{title} {content}".strip()
    
    def preprocess_text(self, text: str) -> str:
        """Basic text preprocessing"""
        # Convert to lowercase
        text = text.lower()
        # Remove extra whitespace
        text = ' '.join(text.split())
        return text
    
    def get_statistics(self) -> Dict:
        """Get dataset statistics"""
        return {
            "num_documents": len(self.documents),
            "num_queries": len(self.queries),
            "num_qrels": len(self.qrels),
            "avg_doc_length": sum(len(self.get_document_text(doc).split()) for doc in self.documents) / len(self.documents) if self.documents else 0
        }
