"""
Data processing module for handling Vaswani dataset and other IR datasets
"""
import os
import json
import pandas as pd
from typing import List, Dict, Tuple, Optional
import requests
from tqdm import tqdm
import xml.etree.ElementTree as ET
from config import Config

class DataProcessor:
    """Data processor for information retrieval datasets"""
    
    def __init__(self):
        self.config = Config()
        self.documents = []
        self.queries = []
        self.qrels = {}  # Query relevance judgments
    
    def download_vaswani_dataset(self, use_real_data: bool = False, dataset_name: str = "cranfield") -> bool:
        """
        Download or create IR dataset

        Args:
            use_real_data: If True, attempt to download real dataset
            dataset_name: Name of dataset to use ("cranfield", "vaswani", "cacm", "sample")
        """
        try:
            if use_real_data:
                return self._download_real_dataset(dataset_name)
            else:
                # Create comprehensive sample dataset for demonstration
                self._create_comprehensive_dataset()
                return True
        except Exception as e:
            print(f"Error downloading dataset: {e}")
            return False

    def _download_real_dataset(self, dataset_name: str) -> bool:
        """Download real IR dataset"""
        try:
            if dataset_name.lower() == "cranfield":
                return self._download_cranfield_dataset()
            elif dataset_name.lower() == "vaswani":
                return self._download_vaswani_dataset()
            elif dataset_name.lower() == "cacm":
                return self._download_cacm_dataset()
            else:
                print(f"Dataset {dataset_name} not supported. Using sample data.")
                self._create_comprehensive_dataset()
                return True
        except Exception as e:
            print(f"Error downloading real dataset: {e}")
            print("Falling back to sample dataset...")
            self._create_comprehensive_dataset()
            return True
    
    def _download_cranfield_dataset(self) -> bool:
        """Download Cranfield dataset (classic IR test collection)"""
        try:
            import urllib.request
            import tarfile
            import os

            print("Downloading Cranfield dataset...")

            # Cranfield dataset URLs (these are example URLs - you may need to find actual sources)
            base_url = "http://ir.dcs.gla.ac.uk/resources/test_collections/cran/"

            # For now, create a larger simulated Cranfield-like dataset
            print("Creating Cranfield-like dataset...")
            self._create_cranfield_like_dataset()
            return True

        except Exception as e:
            print(f"Error downloading Cranfield dataset: {e}")
            return False

    def _download_vaswani_dataset(self) -> bool:
        """Download Vaswani dataset"""
        try:
            print("Creating Vaswani-like dataset...")
            self._create_vaswani_like_dataset()
            return True
        except Exception as e:
            print(f"Error creating Vaswani dataset: {e}")
            return False

    def _download_cacm_dataset(self) -> bool:
        """Download CACM dataset"""
        try:
            print("Creating CACM-like dataset...")
            self._create_cacm_like_dataset()
            return True
        except Exception as e:
            print(f"Error creating CACM dataset: {e}")
            return False

    def _create_comprehensive_dataset(self):
        """Create a comprehensive sample dataset simulating larger IR collection"""

        # Choose which dataset to create based on size preference
        self._create_cranfield_like_dataset()

    def _create_cranfield_like_dataset(self):
        """Create a Cranfield-like dataset with aerodynamics focus"""
        from src.modules.dataset_generator import IRDatasetGenerator

        print("Generating Cranfield-like dataset...")
        generator = IRDatasetGenerator()

        # Generate a substantial dataset (similar to original Cranfield size)
        documents, queries, qrels = generator.generate_cranfield_like_dataset(
            num_docs=1400,  # Original Cranfield had 1400 documents
            num_queries=225  # Original Cranfield had 225 queries
        )

        print(f"Generated {len(documents)} documents and {len(queries)} queries")

        # Save the generated data
        self._save_documents(documents)
        self._save_queries(queries)
        self._save_qrels(qrels)

        # Load into memory
        self.documents = documents
        self.queries = queries
        self.qrels = qrels

    def _create_vaswani_like_dataset(self):
        """Create a Vaswani-like dataset"""
        from src.modules.dataset_generator import IRDatasetGenerator

        print("Generating Vaswani-like dataset...")
        generator = IRDatasetGenerator()

        documents, queries, qrels = generator.generate_vaswani_like_dataset(
            num_docs=11429,  # Original Vaswani collection size
            num_queries=93
        )

        print(f"Generated {len(documents)} documents and {len(queries)} queries")

        self._save_documents(documents)
        self._save_queries(queries)
        self._save_qrels(qrels)

        self.documents = documents
        self.queries = queries
        self.qrels = qrels

    def _create_cacm_like_dataset(self):
        """Create a CACM-like dataset"""
        from src.modules.dataset_generator import IRDatasetGenerator

        print("Generating CACM-like dataset...")
        generator = IRDatasetGenerator()

        documents, queries, qrels = generator.generate_cacm_like_dataset(
            num_docs=3204,  # Original CACM collection size
            num_queries=64
        )

        print(f"Generated {len(documents)} documents and {len(queries)} queries")

        self._save_documents(documents)
        self._save_queries(queries)
        self._save_qrels(qrels)

        self.documents = documents
        self.queries = queries
        self.qrels = qrels

        # Extended document collection (simulating larger Vaswani-like collection)
        sample_docs = [
            {
                "doc_id": "doc_001",
                "title": "Information Retrieval Systems",
                "content": "Information retrieval systems are designed to help users find relevant documents from large collections. These systems use various techniques including indexing, ranking, and relevance feedback. Modern IR systems incorporate machine learning algorithms to improve retrieval effectiveness and user satisfaction."
            },
            {
                "doc_id": "doc_002",
                "title": "Vector Space Model",
                "content": "The vector space model represents documents and queries as vectors in a high-dimensional space. Similarity between documents and queries is computed using cosine similarity or other distance metrics. This model forms the foundation for many modern search engines and recommendation systems."
            },
            {
                "doc_id": "doc_003",
                "title": "Boolean Retrieval",
                "content": "Boolean retrieval systems use Boolean logic to match documents with queries. Users can combine terms using AND, OR, and NOT operators to specify their information needs. While simple, Boolean models provide precise control over query formulation and are still used in specialized domains."
            },
            {
                "doc_id": "doc_004",
                "title": "Probabilistic Models",
                "content": "Probabilistic models in information retrieval estimate the probability that a document is relevant to a query. The BM25 algorithm is a popular probabilistic ranking function that considers term frequency, document frequency, and document length normalization."
            },
            {
                "doc_id": "doc_005",
                "title": "Neural Information Retrieval",
                "content": "Neural approaches to information retrieval use deep learning models to learn representations of documents and queries. These models can capture semantic similarities beyond exact term matching. BERT, sentence transformers, and dense passage retrieval are examples of neural IR methods."
            },
            {
                "doc_id": "doc_006",
                "title": "Text Preprocessing and Indexing",
                "content": "Text preprocessing is a crucial step in information retrieval that involves tokenization, stemming, stop word removal, and normalization. Inverted indices are built to enable efficient retrieval by mapping terms to documents. Modern indexing techniques include distributed indexing and real-time index updates."
            },
            {
                "doc_id": "doc_007",
                "title": "Query Processing and Expansion",
                "content": "Query processing involves parsing user queries, applying linguistic processing, and expanding queries with synonyms or related terms. Query expansion techniques include relevance feedback, pseudo-relevance feedback, and automatic query reformulation using thesauri or word embeddings."
            },
            {
                "doc_id": "doc_008",
                "title": "Evaluation Metrics in Information Retrieval",
                "content": "Information retrieval systems are evaluated using various metrics including precision, recall, F-measure, mean average precision (MAP), and normalized discounted cumulative gain (NDCG). These metrics assess different aspects of retrieval quality and user satisfaction."
            },
            {
                "doc_id": "doc_009",
                "title": "Web Search and Ranking",
                "content": "Web search engines face unique challenges including massive scale, spam detection, and authority assessment. PageRank and other link analysis algorithms help determine document authority. Modern web search combines content relevance with authority signals and user behavior data."
            },
            {
                "doc_id": "doc_010",
                "title": "Machine Learning in Information Retrieval",
                "content": "Machine learning techniques are widely applied in information retrieval for ranking, classification, and clustering. Learning to rank algorithms optimize ranking functions using training data. Deep learning models can learn complex patterns in text and user behavior."
            },
            {
                "doc_id": "doc_011",
                "title": "Cross-Language Information Retrieval",
                "content": "Cross-language information retrieval enables searching documents in different languages from queries in another language. Techniques include machine translation, cross-language word embeddings, and multilingual neural models. This is important for global information access."
            },
            {
                "doc_id": "doc_012",
                "title": "Multimedia Information Retrieval",
                "content": "Multimedia IR deals with retrieving images, videos, and audio content. Content-based retrieval uses visual features, while concept-based approaches use semantic annotations. Multimodal retrieval combines text and visual information for better results."
            },
            {
                "doc_id": "doc_013",
                "title": "Personalized Search and Recommendation",
                "content": "Personalized search systems adapt results based on user profiles, search history, and preferences. Collaborative filtering and content-based filtering are common recommendation techniques. Privacy concerns must be balanced with personalization benefits."
            },
            {
                "doc_id": "doc_014",
                "title": "Real-time Information Retrieval",
                "content": "Real-time IR systems handle streaming data and provide immediate results for time-sensitive queries. Challenges include incremental indexing, result freshness, and scalability. Social media search and news retrieval are typical applications."
            },
            {
                "doc_id": "doc_015",
                "title": "Question Answering Systems",
                "content": "Question answering systems go beyond document retrieval to provide direct answers to user questions. They combine information retrieval with natural language processing and knowledge extraction. Modern QA systems use reading comprehension models and knowledge graphs."
            },
            {
                "doc_id": "doc_016",
                "title": "Enterprise Search Solutions",
                "content": "Enterprise search systems help organizations find information across internal documents, databases, and applications. They must handle diverse data formats, access controls, and integration with existing systems. Federated search and unified interfaces are important features."
            },
            {
                "doc_id": "doc_017",
                "title": "Mobile and Voice Search",
                "content": "Mobile search interfaces present unique challenges due to small screens and touch input. Voice search requires speech recognition and natural language understanding. Context awareness and location-based results are important for mobile users."
            },
            {
                "doc_id": "doc_018",
                "title": "Search Engine Optimization",
                "content": "Search engine optimization (SEO) involves techniques to improve website visibility in search results. White-hat SEO focuses on content quality and user experience, while black-hat techniques attempt to manipulate rankings. Search engines continuously update algorithms to combat spam."
            },
            {
                "doc_id": "doc_019",
                "title": "Information Filtering and Recommendation",
                "content": "Information filtering systems help users manage information overload by selecting relevant content. Collaborative filtering uses user similarity, while content-based filtering analyzes item features. Hybrid approaches combine multiple techniques for better performance."
            },
            {
                "doc_id": "doc_020",
                "title": "Distributed Information Retrieval",
                "content": "Distributed IR systems search across multiple collections or databases. Challenges include collection selection, result merging, and resource allocation. Federated search architectures enable searching heterogeneous information sources through unified interfaces."
            }
        ]
        
        # Extended query collection
        sample_queries = [
            {
                "query_id": "q001",
                "query_text": "information retrieval systems"
            },
            {
                "query_id": "q002",
                "query_text": "vector space model similarity"
            },
            {
                "query_id": "q003",
                "query_text": "boolean logic retrieval"
            },
            {
                "query_id": "q004",
                "query_text": "probabilistic ranking functions"
            },
            {
                "query_id": "q005",
                "query_text": "neural networks deep learning"
            },
            {
                "query_id": "q006",
                "query_text": "text preprocessing tokenization"
            },
            {
                "query_id": "q007",
                "query_text": "query expansion relevance feedback"
            },
            {
                "query_id": "q008",
                "query_text": "evaluation metrics precision recall"
            },
            {
                "query_id": "q009",
                "query_text": "web search ranking algorithms"
            },
            {
                "query_id": "q010",
                "query_text": "machine learning ranking"
            },
            {
                "query_id": "q011",
                "query_text": "cross language retrieval translation"
            },
            {
                "query_id": "q012",
                "query_text": "multimedia image video retrieval"
            },
            {
                "query_id": "q013",
                "query_text": "personalized search recommendation"
            },
            {
                "query_id": "q014",
                "query_text": "real time streaming data"
            },
            {
                "query_id": "q015",
                "query_text": "question answering systems"
            }
        ]
        
        # Extended relevance judgments (qrels) - 2=highly relevant, 1=relevant, 0=not relevant
        sample_qrels = {
            "q001": {"doc_001": 2, "doc_002": 1, "doc_005": 1, "doc_010": 1},
            "q002": {"doc_002": 2, "doc_001": 1, "doc_005": 1},
            "q003": {"doc_003": 2, "doc_001": 1, "doc_007": 1},
            "q004": {"doc_004": 2, "doc_001": 1, "doc_010": 1},
            "q005": {"doc_005": 2, "doc_010": 2, "doc_001": 1},
            "q006": {"doc_006": 2, "doc_001": 1, "doc_007": 1},
            "q007": {"doc_007": 2, "doc_001": 1, "doc_006": 1},
            "q008": {"doc_008": 2, "doc_001": 1, "doc_010": 1},
            "q009": {"doc_009": 2, "doc_001": 1, "doc_010": 1, "doc_018": 1},
            "q010": {"doc_010": 2, "doc_001": 1, "doc_005": 1, "doc_008": 1},
            "q011": {"doc_011": 2, "doc_001": 1, "doc_005": 1},
            "q012": {"doc_012": 2, "doc_001": 1},
            "q013": {"doc_013": 2, "doc_019": 2, "doc_001": 1, "doc_010": 1},
            "q014": {"doc_014": 2, "doc_001": 1, "doc_010": 1},
            "q015": {"doc_015": 2, "doc_001": 1, "doc_005": 1, "doc_010": 1}
        }
        
        # Save to files
        self._save_documents(sample_docs)
        self._save_queries(sample_queries)
        self._save_qrels(sample_qrels)
        
        # Load into memory
        self.documents = sample_docs
        self.queries = sample_queries
        self.qrels = sample_qrels
    
    def _save_documents(self, documents: List[Dict]):
        """Save documents to file"""
        doc_path = os.path.join(self.config.DATA_DIR, "documents.json")
        with open(doc_path, 'w', encoding='utf-8') as f:
            json.dump(documents, f, indent=2, ensure_ascii=False)
    
    def _save_queries(self, queries: List[Dict]):
        """Save queries to file"""
        query_path = os.path.join(self.config.DATA_DIR, "queries.json")
        with open(query_path, 'w', encoding='utf-8') as f:
            json.dump(queries, f, indent=2, ensure_ascii=False)
    
    def _save_qrels(self, qrels: Dict):
        """Save relevance judgments to file"""
        qrels_path = os.path.join(self.config.DATA_DIR, "qrels.json")
        with open(qrels_path, 'w', encoding='utf-8') as f:
            json.dump(qrels, f, indent=2, ensure_ascii=False)
    
    def load_dataset(self) -> Tuple[List[Dict], List[Dict], Dict]:
        """Load dataset from files"""
        try:
            # Load documents
            doc_path = os.path.join(self.config.DATA_DIR, "documents.json")
            with open(doc_path, 'r', encoding='utf-8') as f:
                self.documents = json.load(f)
            
            # Load queries
            query_path = os.path.join(self.config.DATA_DIR, "queries.json")
            with open(query_path, 'r', encoding='utf-8') as f:
                self.queries = json.load(f)
            
            # Load qrels
            qrels_path = os.path.join(self.config.DATA_DIR, "qrels.json")
            with open(qrels_path, 'r', encoding='utf-8') as f:
                self.qrels = json.load(f)
            
            return self.documents, self.queries, self.qrels
            
        except FileNotFoundError:
            print("Dataset files not found. Please download the dataset first.")
            return [], [], {}
    
    def get_document_text(self, doc: Dict) -> str:
        """Extract full text from document"""
        title = doc.get('title', '')
        content = doc.get('content', '')
        return f"{title} {content}".strip()
    
    def preprocess_text(self, text: str) -> str:
        """Basic text preprocessing"""
        # Convert to lowercase
        text = text.lower()
        # Remove extra whitespace
        text = ' '.join(text.split())
        return text
    
    def get_statistics(self) -> Dict:
        """Get dataset statistics"""
        return {
            "num_documents": len(self.documents),
            "num_queries": len(self.queries),
            "num_qrels": len(self.qrels),
            "avg_doc_length": sum(len(self.get_document_text(doc).split()) for doc in self.documents) / len(self.documents) if self.documents else 0
        }
