"""
Test script for individual modules
"""
import sys
import traceback
from config import Config
from src.modules.data_processor import DataProcessor
from src.modules.sparse_indexer import SparseIndexer
from src.modules.dense_indexer import DenseIndexer
from src.modules.bm25_retriever import BM25Retriever
from src.modules.dense_retriever import DenseRetriever
from src.modules.llm_reranker import LLMReranker
from src.modules.evaluator import IREvaluator

def test_data_processor():
    """Test data processor module"""
    print("Testing Data Processor...")
    try:
        processor = DataProcessor()
        
        # Test dataset creation
        success = processor.download_vaswani_dataset()
        assert success, "Failed to create dataset"
        
        # Test dataset loading
        documents, queries, qrels = processor.load_dataset()
        assert len(documents) > 0, "No documents loaded"
        assert len(queries) > 0, "No queries loaded"
        assert len(qrels) > 0, "No qrels loaded"
        
        # Test statistics
        stats = processor.get_statistics()
        assert stats['num_documents'] == len(documents)
        
        print("✓ Data Processor test passed")
        return True
        
    except Exception as e:
        print(f"✗ Data Processor test failed: {e}")
        traceback.print_exc()
        return False

def test_sparse_indexer():
    """Test sparse indexer module"""
    print("Testing Sparse Indexer...")
    try:
        processor = DataProcessor()
        documents, _, _ = processor.load_dataset()
        
        indexer = SparseIndexer()
        
        # Test index building
        success = indexer.build_index(documents)
        assert success, "Failed to build index"
        
        # Test index properties
        assert indexer.get_vocabulary_size() > 0, "Empty vocabulary"
        assert indexer.total_documents == len(documents)
        
        # Test term lookup
        doc_id = documents[0]['doc_id']
        doc_length = indexer.get_document_length(doc_id)
        assert doc_length > 0, "Invalid document length"
        
        print("✓ Sparse Indexer test passed")
        return True
        
    except Exception as e:
        print(f"✗ Sparse Indexer test failed: {e}")
        traceback.print_exc()
        return False

def test_bm25_retriever():
    """Test BM25 retriever module"""
    print("Testing BM25 Retriever...")
    try:
        processor = DataProcessor()
        documents, queries, _ = processor.load_dataset()
        
        # Build index
        indexer = SparseIndexer()
        indexer.build_index(documents)
        
        # Test retriever
        retriever = BM25Retriever()
        retriever.set_indexer(indexer)
        
        # Test single query
        test_query = queries[0]['query_text']
        results = retriever.retrieve(test_query, top_k=5)
        
        assert len(results) > 0, "No results returned"
        assert all(isinstance(score, (int, float)) for _, score in results), "Invalid scores"
        
        # Test batch retrieval
        batch_results = retriever.batch_retrieve(queries, top_k=5)
        assert len(batch_results) == len(queries), "Batch results count mismatch"
        
        print("✓ BM25 Retriever test passed")
        return True
        
    except Exception as e:
        print(f"✗ BM25 Retriever test failed: {e}")
        traceback.print_exc()
        return False

def test_dense_indexer():
    """Test dense indexer module"""
    print("Testing Dense Indexer...")
    try:
        processor = DataProcessor()
        documents, _, _ = processor.load_dataset()
        
        indexer = DenseIndexer()
        
        # Test index building
        success = indexer.build_index(documents)
        assert success, "Failed to build dense index"
        
        # Test index properties
        stats = indexer.get_index_statistics()
        assert stats['num_documents'] == len(documents)
        assert stats['embedding_dimension'] > 0
        
        print("✓ Dense Indexer test passed")
        return True
        
    except Exception as e:
        print(f"✗ Dense Indexer test failed: {e}")
        traceback.print_exc()
        return False

def test_dense_retriever():
    """Test dense retriever module"""
    print("Testing Dense Retriever...")
    try:
        processor = DataProcessor()
        documents, queries, _ = processor.load_dataset()
        
        # Build dense index
        indexer = DenseIndexer()
        indexer.build_index(documents)
        
        # Test retriever
        retriever = DenseRetriever()
        retriever.set_indexer(indexer)
        
        # Test single query
        test_query = queries[0]['query_text']
        results = retriever.retrieve(test_query, top_k=5)
        
        assert len(results) > 0, "No results returned"
        assert all(isinstance(score, (int, float)) for _, score in results), "Invalid scores"
        
        print("✓ Dense Retriever test passed")
        return True
        
    except Exception as e:
        print(f"✗ Dense Retriever test failed: {e}")
        traceback.print_exc()
        return False

def test_evaluator():
    """Test evaluator module"""
    print("Testing Evaluator...")
    try:
        processor = DataProcessor()
        documents, queries, qrels = processor.load_dataset()
        
        # Create mock retrieval results
        mock_results = {}
        for query in queries:
            query_id = query['query_id']
            # Mock some results
            mock_results[query_id] = [
                (documents[0]['doc_id'], 0.9),
                (documents[1]['doc_id'], 0.7),
                (documents[2]['doc_id'], 0.5)
            ]
        
        evaluator = IREvaluator()
        
        # Test batch evaluation
        eval_results = evaluator.evaluate_batch(mock_results, qrels)
        
        assert 'average_metrics' in eval_results
        assert 'MAP' in eval_results['average_metrics']
        assert 'query_results' in eval_results
        
        # Test individual metrics
        retrieved_docs = ['doc_001', 'doc_002', 'doc_003']
        relevant_docs = ['doc_001', 'doc_003']
        
        precision = evaluator.precision_at_k(retrieved_docs, relevant_docs, 3)
        assert 0 <= precision <= 1, "Invalid precision value"
        
        print("✓ Evaluator test passed")
        return True
        
    except Exception as e:
        print(f"✗ Evaluator test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("="*60)
    print(" IR EXPERIMENT PLATFORM - MODULE TESTS")
    print("="*60)
    
    # Validate configuration
    try:
        Config.validate_config()
        print("✓ Configuration validated\n")
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return
    
    tests = [
        test_data_processor,
        test_sparse_indexer,
        test_bm25_retriever,
        test_dense_indexer,
        test_dense_retriever,
        test_evaluator
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} crashed: {e}")
        print()
    
    print("="*60)
    print(f" TEST RESULTS: {passed}/{total} PASSED")
    print("="*60)
    
    if passed == total:
        print("🎉 All tests passed! The platform is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
