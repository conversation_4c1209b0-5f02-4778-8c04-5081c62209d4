# Information Retrieval Experiment Platform

A comprehensive information retrieval experiment platform built with LangGraph, featuring multiple retrieval methods, LLM-based reranking, and standard IR evaluation metrics.

## Features

### Core Modules
1. **Sparse Indexing** - Traditional inverted index construction
2. **Dense Indexing** - Sentence-BERT based semantic embeddings with FAISS
3. **BM25 Retrieval** - Classic probabilistic retrieval model
4. **Dense Retrieval** - Semantic similarity search using embeddings
5. **LLM Reranking** - Large language model based result reranking
6. **Evaluation** - Standard IR metrics (NDCG, MAP, Precision@K, etc.)

### Workflow Integration
- **LangGraph Integration** - All modules connected via LangGraph workflow
- **Configurable Pipeline** - Easy to modify and extend
- **Batch Processing** - Efficient handling of multiple queries

## Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your OpenRouter API key and proxy settings
```

### 2. Configuration

Edit `.env` file:
```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Model Configuration
DEFAULT_MODEL=openai/gpt-4-1106-preview
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Proxy Configuration (if needed)
HTTP_PROXY=http://127.0.0.1:1087
HTTPS_PROXY=http://127.0.0.1:1087
```

### 3. Run Demo

```bash
python demo.py
```

## Demo Workflow

The demo demonstrates the complete pipeline:

1. **Data Loading** - Loads Vaswani-like test collection
2. **Index Building** - Constructs both sparse and dense indices
3. **BM25 Retrieval** - Retrieves top-100 documents using BM25
4. **LLM Reranking** - Reranks top-10 results using GPT-4
5. **Evaluation** - Computes standard IR metrics

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Loading  │───▶│  Index Building  │───▶│   Retrieval     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Evaluation    │◀───│  LLM Reranking   │◀───│  BM25 Results   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Modules

### Data Processor (`src/modules/data_processor.py`)
- Handles dataset loading and preprocessing
- Creates sample Vaswani-like collection for demo
- Supports custom dataset integration

### Sparse Indexer (`src/modules/sparse_indexer.py`)
- Builds inverted index for term-based retrieval
- Supports TF-IDF weighting
- Efficient storage and retrieval

### Dense Indexer (`src/modules/dense_indexer.py`)
- Uses sentence-transformers for semantic embeddings
- FAISS integration for efficient similarity search
- Supports various embedding models

### BM25 Retriever (`src/modules/bm25_retriever.py`)
- Implements BM25 ranking algorithm
- Configurable parameters (k1, b)
- Batch query processing

### LLM Reranker (`src/modules/llm_reranker.py`)
- OpenRouter API integration
- List-wise ranking approach
- Supports multiple LLM models

### Evaluator (`src/modules/evaluator.py`)
- Standard IR metrics: NDCG, MAP, Precision@K, Recall@K
- Batch evaluation support
- System comparison utilities

## Configuration

The platform uses environment variables for configuration:

- `OPENROUTER_API_KEY` - Your OpenRouter API key
- `DEFAULT_MODEL` - LLM model for reranking
- `EMBEDDING_MODEL` - Sentence transformer model
- `HTTP_PROXY/HTTPS_PROXY` - Proxy settings
- `TOP_K_RETRIEVAL` - Number of documents to retrieve
- `TOP_K_RERANK` - Number of documents to rerank

## Extending the Platform

### Adding New Retrieval Methods
1. Create new retriever class in `src/modules/`
2. Implement `retrieve()` and `batch_retrieve()` methods
3. Add to workflow in `src/workflow.py`

### Adding New Evaluation Metrics
1. Extend `IREvaluator` class
2. Add metric calculation methods
3. Update evaluation workflow

### Custom Datasets
1. Extend `DataProcessor` class
2. Implement dataset-specific loading methods
3. Ensure compatibility with existing pipeline

## Results

The demo outputs:
- Workflow execution log
- BM25 retrieval performance
- LLM reranking performance
- System comparison metrics
- Detailed per-query results

Results are saved to `results/demo_results.json` for further analysis.

## Requirements

- Python 3.8+
- OpenRouter API access
- Internet connection (for model downloads)
- Optional: GPU for faster embedding computation

## License

MIT License - see LICENSE file for details.
