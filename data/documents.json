[{"doc_id": "doc_001", "title": "Information Retrieval Systems", "content": "Information retrieval systems are designed to help users find relevant documents from large collections. These systems use various techniques including indexing, ranking, and relevance feedback."}, {"doc_id": "doc_002", "title": "Vector Space Model", "content": "The vector space model represents documents and queries as vectors in a high-dimensional space. Similarity between documents and queries is computed using cosine similarity or other distance metrics."}, {"doc_id": "doc_003", "title": "Boolean Retrieval", "content": "Boolean retrieval systems use Boolean logic to match documents with queries. Users can combine terms using AND, OR, and NOT operators to specify their information needs."}, {"doc_id": "doc_004", "title": "Probabilistic Models", "content": "Probabilistic models in information retrieval estimate the probability that a document is relevant to a query. The BM25 algorithm is a popular probabilistic ranking function."}, {"doc_id": "doc_005", "title": "Neural Information Retrieval", "content": "Neural approaches to information retrieval use deep learning models to learn representations of documents and queries. These models can capture semantic similarities beyond exact term matching."}]