"""
Demo script for the Information Retrieval Experiment Platform
Demonstrates: Sparse Index -> BM25 Retrieval -> LLM Reranking -> Evaluation
"""
import os
import json
from config import Config
from src.workflow import IRWorkflow

def print_separator(title: str):
    """Print a formatted separator"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_results(results: dict, title: str):
    """Print evaluation results in a formatted way"""
    print_separator(title)
    
    if 'error' in results:
        print(f"Error: {results['error']}")
        return
    
    print(f"Number of queries evaluated: {results['num_queries']}")
    print("\nAverage Metrics:")
    print("-" * 40)
    
    metrics = results['average_metrics']
    for metric, value in metrics.items():
        print(f"{metric:12}: {value:.4f}")

def print_query_details(results: dict, query_limit: int = 2):
    """Print detailed results for individual queries"""
    if 'query_results' not in results:
        return
    
    print(f"\nDetailed Results (showing first {query_limit} queries):")
    print("-" * 50)
    
    for i, query_result in enumerate(results['query_results'][:query_limit]):
        query_id = query_result['query_id']
        print(f"\nQuery {query_id}:")
        print(f"  Retrieved: {query_result['num_retrieved']}")
        print(f"  Relevant: {query_result['num_relevant']}")
        
        metrics = query_result['metrics']
        for metric, value in metrics.items():
            print(f"  {metric:10}: {value:.4f}")

def compare_systems(bm25_results: dict, reranked_results: dict):
    """Compare BM25 and reranked results"""
    print_separator("SYSTEM COMPARISON: BM25 vs LLM Reranked")
    
    if 'error' in bm25_results or 'error' in reranked_results:
        print("Cannot compare systems due to errors in evaluation")
        return
    
    bm25_metrics = bm25_results['average_metrics']
    reranked_metrics = reranked_results['average_metrics']
    
    print(f"{'Metric':<12} {'BM25':<10} {'Reranked':<10} {'Improvement':<12}")
    print("-" * 50)
    
    for metric in bm25_metrics.keys():
        if metric in reranked_metrics:
            bm25_val = bm25_metrics[metric]
            reranked_val = reranked_metrics[metric]
            
            if bm25_val > 0:
                improvement = ((reranked_val - bm25_val) / bm25_val) * 100
                improvement_str = f"{improvement:+.1f}%"
            else:
                improvement_str = "N/A"
            
            print(f"{metric:<12} {bm25_val:<10.4f} {reranked_val:<10.4f} {improvement_str:<12}")

def main():
    """Main demo function"""
    print_separator("IR EXPERIMENT PLATFORM DEMO")
    print("This demo demonstrates the complete IR pipeline:")
    print("1. Data Loading (Vaswani-like dataset)")
    print("2. Sparse Index Building")
    print("3. Dense Index Building") 
    print("4. BM25 Retrieval")
    print("5. Dense Retrieval")
    print("6. LLM Reranking")
    print("7. Evaluation")
    
    # Validate configuration
    try:
        Config.validate_config()
        print("\n✓ Configuration validated successfully")
    except Exception as e:
        print(f"\n✗ Configuration error: {e}")
        return
    
    # Initialize workflow
    print("\n📊 Initializing IR Workflow...")
    workflow = IRWorkflow()
    
    # Run experiment
    print("\n🚀 Running IR Experiment...")
    try:
        results = workflow.run_experiment(
            top_k_retrieval=100,  # Retrieve top 100 with BM25
            top_k_rerank=10       # Rerank top 10
        )
        
        if results["status"] == "error":
            print(f"\n✗ Experiment failed: {results['messages'][-1]}")
            return
        
        print("\n✓ Experiment completed successfully!")
        
        # Print workflow messages
        print_separator("WORKFLOW EXECUTION LOG")
        for message in results["messages"]:
            print(f"• {message}")
        
        # Display BM25 results
        if "bm25_evaluation" in results:
            print_results(results["bm25_evaluation"], "BM25 RETRIEVAL RESULTS")
            print_query_details(results["bm25_evaluation"])
        
        # Display reranked results
        if "reranked_evaluation" in results:
            print_results(results["reranked_evaluation"], "LLM RERANKED RESULTS")
            print_query_details(results["reranked_evaluation"])
        
        # Compare systems
        if "bm25_evaluation" in results and "reranked_evaluation" in results:
            compare_systems(results["bm25_evaluation"], results["reranked_evaluation"])
        
        # Display dense results if available
        if "dense_evaluation" in results:
            print_results(results["dense_evaluation"], "DENSE RETRIEVAL RESULTS")
        
        # Save results
        results_file = os.path.join(Config.RESULTS_DIR, "demo_results.json")
        with open(results_file, 'w') as f:
            # Convert results to JSON-serializable format
            json_results = {
                "status": results["status"],
                "messages": [str(msg) for msg in results["messages"]],  # Convert messages to strings
                "bm25_evaluation": results.get("bm25_evaluation", {}),
                "reranked_evaluation": results.get("reranked_evaluation", {}),
                "dense_evaluation": results.get("dense_evaluation", {})
            }
            json.dump(json_results, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        print_separator("DEMO COMPLETED SUCCESSFULLY")
        print("Key findings:")
        
        if "bm25_evaluation" in results and "reranked_evaluation" in results:
            bm25_map = results["bm25_evaluation"]["average_metrics"].get("MAP", 0)
            reranked_map = results["reranked_evaluation"]["average_metrics"].get("MAP", 0)
            
            if reranked_map > bm25_map:
                improvement = ((reranked_map - bm25_map) / bm25_map) * 100
                print(f"• LLM reranking improved MAP by {improvement:.1f}%")
            else:
                print("• LLM reranking did not improve MAP significantly")
        
        print("• Experiment pipeline executed successfully")
        print("• All modules integrated properly with LangGraph")
        
    except Exception as e:
        print(f"\n✗ Experiment failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
