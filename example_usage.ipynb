{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Information Retrieval Experiment Platform - Usage Example\n", "\n", "This notebook demonstrates how to use the IR experiment platform for custom experiments."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary modules\n", "from config import Config\n", "from src.workflow import IRWorkflow\n", "from src.modules.data_processor import DataProcessor\n", "from src.modules.sparse_indexer import SparseIndexer\n", "from src.modules.bm25_retriever import BM25Retriever\n", "from src.modules.llm_reranker import LLMReranker\n", "from src.modules.evaluator import IREvaluator\n", "\n", "import json\n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load and Explore Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize data processor\n", "data_processor = DataProcessor()\n", "\n", "# Load dataset\n", "documents, queries, qrels = data_processor.load_dataset()\n", "\n", "print(f\"Loaded {len(documents)} documents, {len(queries)} queries\")\n", "print(f\"Dataset statistics: {data_processor.get_statistics()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display sample documents\n", "print(\"Sample Documents:\")\n", "for i, doc in enumerate(documents[:2]):\n", "    print(f\"\\nDocument {i+1}:\")\n", "    print(f\"ID: {doc['doc_id']}\")\n", "    print(f\"Title: {doc['title']}\")\n", "    print(f\"Content: {doc['content'][:100]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Individual Module Testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test BM25 retrieval\n", "sparse_indexer = SparseIndexer()\n", "sparse_indexer.build_index(documents)\n", "\n", "bm25_retriever = BM25Retriever()\n", "bm25_retriever.set_indexer(sparse_indexer)\n", "\n", "# Test query\n", "test_query = \"information retrieval systems\"\n", "results = bm25_retriever.retrieve(test_query, top_k=5)\n", "\n", "print(f\"BM25 Results for '{test_query}':\")\n", "for doc_id, score in results:\n", "    doc = next(d for d in documents if d['doc_id'] == doc_id)\n", "    print(f\"  {doc_id}: {score:.4f} - {doc['title']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Complete Workflow Execution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run complete workflow\n", "workflow = IRWorkflow()\n", "results = workflow.run_experiment(top_k_retrieval=100, top_k_rerank=10)\n", "\n", "print(f\"Workflow Status: {results['status']}\")\n", "print(\"\\nExecution Log:\")\n", "for message in results['messages']:\n", "    print(f\"  • {message}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Results Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comparison dataframe\n", "import pandas as pd\n", "\n", "if 'bm25_evaluation' in results and 'reranked_evaluation' in results:\n", "    bm25_metrics = results['bm25_evaluation']['average_metrics']\n", "    reranked_metrics = results['reranked_evaluation']['average_metrics']\n", "    \n", "    comparison_data = []\n", "    for metric in bm25_metrics.keys():\n", "        if metric in reranked_metrics:\n", "            bm25_val = bm25_metrics[metric]\n", "            reranked_val = reranked_metrics[metric]\n", "            improvement = ((reranked_val - bm25_val) / bm25_val * 100) if bm25_val > 0 else 0\n", "            \n", "            comparison_data.append({\n", "                'Metric': metric,\n", "                'BM25': bm25_val,\n", "                'LLM Reranked': reranked_val,\n", "                'Improvement (%)': improvement\n", "            })\n", "    \n", "    df = pd.DataFrame(comparison_data)\n", "    print(\"System Comparison:\")\n", "    print(df.round(4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Plot comparison\n", "if 'df' in locals():\n", "    plt.figure(figsize=(12, 6))\n", "    \n", "    # Select key metrics for visualization\n", "    key_metrics = ['P@1', 'P@5', 'P@10', 'NDCG@5', 'NDCG@10', 'MAP']\n", "    df_viz = df[df['Metric'].isin(key_metrics)]\n", "    \n", "    x = range(len(df_viz))\n", "    width = 0.35\n", "    \n", "    plt.bar([i - width/2 for i in x], df_viz['BM25'], width, label='BM25', alpha=0.8)\n", "    plt.bar([i + width/2 for i in x], df_viz['LLM Reranked'], width, label='LLM Reranked', alpha=0.8)\n", "    \n", "    plt.xlabel('Metrics')\n", "    plt.ylabel('Score')\n", "    plt.title('BM<PERSON> vs LLM Reranked Performance Comparison')\n", "    plt.xticks(x, df_viz['Metric'])\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Custom Experiments"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example: Test different BM25 parameters\n", "from src.modules.bm25_retriever import BM25Retriever\n", "\n", "# Test different k1 and b parameters\n", "param_combinations = [\n", "    (1.2, 0.75),  # <PERSON><PERSON><PERSON>\n", "    (1.5, 0.75),  # Higher k1\n", "    (1.2, 0.5),   # Lower b\n", "    (2.0, 0.9)    # Higher both\n", "]\n", "\n", "evaluator = IREvaluator()\n", "param_results = []\n", "\n", "for k1, b in param_combinations:\n", "    retriever = BM25Retriever(k1=k1, b=b)\n", "    retriever.set_indexer(sparse_indexer)\n", "    \n", "    # Retrieve for all queries\n", "    retrieval_results = retriever.batch_retrieve(queries, top_k=10)\n", "    \n", "    # Evaluate\n", "    eval_results = evaluator.evaluate_batch(retrieval_results, qrels)\n", "    \n", "    param_results.append({\n", "        'k1': k1,\n", "        'b': b,\n", "        'MAP': eval_results['average_metrics']['MAP'],\n", "        'NDCG@10': eval_results['average_metrics']['NDCG@10']\n", "    })\n", "\n", "param_df = pd.DataFrame(param_results)\n", "print(\"BM25 Parameter Tuning Results:\")\n", "print(param_df.round(4))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}