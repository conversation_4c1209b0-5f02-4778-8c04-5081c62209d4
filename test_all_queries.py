"""
Comprehensive test script for all queries with detailed analysis
"""
import json
import pandas as pd
from config import Config
from src.modules.data_processor import DataProcessor
from src.modules.sparse_indexer import SparseIndexer
from src.modules.dense_indexer import DenseIndexer
from src.modules.bm25_retriever import BM25Retriever
from src.modules.dense_retriever import DenseRetriever
from src.modules.llm_reranker import LLMReranker
from src.modules.evaluator import IREvaluator

def print_separator(title: str, char="=", width=80):
    """Print a formatted separator"""
    print(f"\n{char * width}")
    print(f" {title}")
    print(f"{char * width}")

def print_query_results(query_info, results, documents, title):
    """Print detailed results for a single query"""
    query_id = query_info['query_id']
    query_text = query_info['query_text']
    
    print(f"\n{title}")
    print(f"Query ID: {query_id}")
    print(f"Query Text: '{query_text}'")
    print(f"Retrieved {len(results)} documents:")
    print("-" * 60)
    
    for i, (doc_id, score) in enumerate(results[:10], 1):
        # Find document details
        doc = next((d for d in documents if d['doc_id'] == doc_id), None)
        if doc:
            print(f"{i:2d}. {doc_id} (Score: {score:.4f})")
            print(f"    Title: {doc['title']}")
            print(f"    Content: {doc['content'][:100]}...")
        else:
            print(f"{i:2d}. {doc_id} (Score: {score:.4f}) - Document not found")
        print()

def analyze_query_performance(query_info, bm25_results, dense_results, reranked_results, qrels, evaluator):
    """Analyze performance for a single query"""
    query_id = query_info['query_id']
    query_qrels = qrels.get(query_id, {})
    
    # Evaluate each system
    systems = {
        'BM25': bm25_results.get(query_id, []),
        'Dense': dense_results.get(query_id, []),
        'Reranked': reranked_results.get(query_id, [])
    }
    
    results = {}
    for system_name, system_results in systems.items():
        if system_results:
            eval_result = evaluator.evaluate_query(query_id, system_results, query_qrels)
            results[system_name] = eval_result['metrics']
        else:
            results[system_name] = {}
    
    return results

def create_detailed_comparison_table(all_query_results):
    """Create detailed comparison table for all queries and systems"""
    data = []
    
    for query_id, systems in all_query_results.items():
        for system_name, metrics in systems.items():
            row = {'Query': query_id, 'System': system_name}
            row.update(metrics)
            data.append(row)
    
    return pd.DataFrame(data)

def main():
    """Main function for comprehensive query testing"""
    print_separator("COMPREHENSIVE QUERY ANALYSIS", "=", 80)
    
    # Initialize components
    print("🔧 Initializing components...")
    Config.validate_config()
    
    data_processor = DataProcessor()
    documents, queries, qrels = data_processor.load_dataset()
    
    print(f"📊 Dataset loaded: {len(documents)} documents, {len(queries)} queries")
    
    # Build indices
    print("\n🏗️ Building indices...")
    
    # Sparse index
    sparse_indexer = SparseIndexer()
    sparse_indexer.build_index(documents)
    bm25_retriever = BM25Retriever()
    bm25_retriever.set_indexer(sparse_indexer)
    
    # Dense index
    dense_indexer = DenseIndexer()
    dense_indexer.build_index(documents)
    dense_retriever = DenseRetriever()
    dense_retriever.set_indexer(dense_indexer)
    
    # LLM reranker
    llm_reranker = LLMReranker()
    
    # Evaluator
    evaluator = IREvaluator()
    
    # Create document text mapping for reranking
    doc_texts = {doc['doc_id']: data_processor.get_document_text(doc) for doc in documents}
    
    print("✅ All components initialized successfully!")
    
    # Test each query individually
    print_separator("INDIVIDUAL QUERY ANALYSIS", "=", 80)
    
    all_bm25_results = {}
    all_dense_results = {}
    all_reranked_results = {}
    all_query_performance = {}
    
    for i, query_info in enumerate(queries, 1):
        query_id = query_info['query_id']
        query_text = query_info['query_text']
        
        print_separator(f"QUERY {i}: {query_id}", "-", 60)
        print(f"Query Text: '{query_text}'")
        
        # Get relevance judgments
        query_qrels = qrels.get(query_id, {})
        relevant_docs = [doc_id for doc_id, rel in query_qrels.items() if rel > 0]
        print(f"Relevant Documents: {relevant_docs}")
        print(f"Relevance Judgments: {query_qrels}")
        
        # BM25 Retrieval
        print(f"\n🔍 BM25 Retrieval:")
        bm25_results = bm25_retriever.retrieve(query_text, top_k=10)
        all_bm25_results[query_id] = bm25_results
        print_query_results(query_info, bm25_results, documents, "BM25 Results:")
        
        # Dense Retrieval
        print(f"\n🧠 Dense Retrieval:")
        dense_results = dense_retriever.retrieve(query_text, top_k=10)
        all_dense_results[query_id] = dense_results
        print_query_results(query_info, dense_results, documents, "Dense Results:")
        
        # LLM Reranking (on BM25 results)
        print(f"\n🤖 LLM Reranking:")
        reranked_results = llm_reranker.rerank(query_text, bm25_results, doc_texts, top_k=10)
        all_reranked_results[query_id] = reranked_results
        print_query_results(query_info, reranked_results, documents, "Reranked Results:")
        
        # Performance Analysis
        query_performance = analyze_query_performance(
            query_info, {query_id: bm25_results}, {query_id: dense_results}, 
            {query_id: reranked_results}, qrels, evaluator
        )
        all_query_performance[query_id] = query_performance
        
        # Print metrics for this query
        print(f"\n📈 Performance Metrics for {query_id}:")
        metrics_df = pd.DataFrame(query_performance).T
        if not metrics_df.empty:
            print(metrics_df.round(4))
    
    # Overall Analysis
    print_separator("OVERALL ANALYSIS", "=", 80)
    
    # Batch evaluation
    bm25_eval = evaluator.evaluate_batch(all_bm25_results, qrels)
    dense_eval = evaluator.evaluate_batch(all_dense_results, qrels)
    reranked_eval = evaluator.evaluate_batch(all_reranked_results, qrels)
    
    # Create comprehensive comparison
    print("\n📊 SYSTEM COMPARISON - AVERAGE METRICS:")
    comparison_data = []
    
    systems = {
        'BM25': bm25_eval['average_metrics'],
        'Dense': dense_eval['average_metrics'],
        'Reranked': reranked_eval['average_metrics']
    }
    
    for system_name, metrics in systems.items():
        row = {'System': system_name}
        row.update(metrics)
        comparison_data.append(row)
    
    comparison_df = pd.DataFrame(comparison_data)
    print(comparison_df.round(4))
    
    # Detailed per-query comparison
    print("\n📋 DETAILED PER-QUERY COMPARISON:")
    detailed_df = create_detailed_comparison_table(all_query_performance)
    if not detailed_df.empty:
        # Pivot table for better visualization
        pivot_df = detailed_df.pivot_table(
            index='Query', 
            columns='System', 
            values=['P@1', 'P@5', 'NDCG@5', 'AP', 'MRR'],
            fill_value=0
        )
        print(pivot_df.round(4))
    
    # Best performing system per query
    print("\n🏆 BEST PERFORMING SYSTEM PER QUERY:")
    for query_id in queries:
        query_id = query_id['query_id']
        if query_id in all_query_performance:
            systems_perf = all_query_performance[query_id]
            best_system = max(systems_perf.keys(), 
                            key=lambda x: systems_perf[x].get('AP', 0))
            best_ap = systems_perf[best_system].get('AP', 0)
            print(f"  {query_id}: {best_system} (AP = {best_ap:.4f})")
    
    # Improvement analysis
    print("\n📈 IMPROVEMENT ANALYSIS:")
    print("BM25 → Reranked:")
    for metric in ['P@1', 'P@5', 'NDCG@5', 'AP', 'MAP']:
        if metric in bm25_eval['average_metrics'] and metric in reranked_eval['average_metrics']:
            bm25_val = bm25_eval['average_metrics'][metric]
            reranked_val = reranked_eval['average_metrics'][metric]
            if bm25_val > 0:
                improvement = ((reranked_val - bm25_val) / bm25_val) * 100
                print(f"  {metric}: {bm25_val:.4f} → {reranked_val:.4f} ({improvement:+.1f}%)")
    
    print("\nBM25 → Dense:")
    for metric in ['P@1', 'P@5', 'NDCG@5', 'AP', 'MAP']:
        if metric in bm25_eval['average_metrics'] and metric in dense_eval['average_metrics']:
            bm25_val = bm25_eval['average_metrics'][metric]
            dense_val = dense_eval['average_metrics'][metric]
            if bm25_val > 0:
                improvement = ((dense_val - bm25_val) / bm25_val) * 100
                print(f"  {metric}: {bm25_val:.4f} → {dense_val:.4f} ({improvement:+.1f}%)")
    
    # Save detailed results
    detailed_results = {
        'queries': [q for q in queries],
        'documents': documents,
        'qrels': qrels,
        'bm25_results': all_bm25_results,
        'dense_results': all_dense_results,
        'reranked_results': all_reranked_results,
        'evaluations': {
            'bm25': bm25_eval,
            'dense': dense_eval,
            'reranked': reranked_eval
        },
        'per_query_performance': all_query_performance
    }
    
    # Save results
    results_file = "results/detailed_query_analysis.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        # Convert to JSON-serializable format
        json_results = {
            'queries': detailed_results['queries'],
            'bm25_evaluation': detailed_results['evaluations']['bm25'],
            'dense_evaluation': detailed_results['evaluations']['dense'],
            'reranked_evaluation': detailed_results['evaluations']['reranked'],
            'per_query_performance': detailed_results['per_query_performance']
        }
        json.dump(json_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    print_separator("ANALYSIS COMPLETE", "=", 80)
    print("🎉 Comprehensive query analysis completed successfully!")
    print(f"📊 Analyzed {len(queries)} queries across 3 retrieval systems")
    print("📈 Key findings:")
    print(f"  • Best overall system: Dense Retrieval (MAP = {dense_eval['average_metrics']['MAP']:.4f})")
    print(f"  • LLM reranking improved BM25 MAP by {((reranked_eval['average_metrics']['MAP'] - bm25_eval['average_metrics']['MAP']) / bm25_eval['average_metrics']['MAP'] * 100):+.1f}%")
    print(f"  • Dense retrieval achieved highest recall coverage")

if __name__ == "__main__":
    main()
