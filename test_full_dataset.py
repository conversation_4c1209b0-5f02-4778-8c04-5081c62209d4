"""
Full dataset evaluation script
Tests the IR platform with large-scale datasets similar to standard IR test collections
"""
import os
import time
import json
import argparse
from config import Config
from src.modules.data_processor import DataProcessor
from src.modules.sparse_indexer import SparseIndexer
from src.modules.dense_indexer import DenseIndexer
from src.modules.bm25_retriever import BM25Retriever
from src.modules.dense_retriever import DenseRetriever
from src.modules.llm_reranker import LLMReranker
from src.modules.evaluator import IREvaluator

def print_separator(title: str, char="=", width=80):
    """Print a formatted separator"""
    print(f"\n{char * width}")
    print(f" {title}")
    print(f"{char * width}")

def print_progress(current: int, total: int, prefix: str = "Progress"):
    """Print progress bar"""
    percent = (current / total) * 100
    bar_length = 50
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    print(f'\r{prefix}: |{bar}| {percent:.1f}% ({current}/{total})', end='', flush=True)
    if current == total:
        print()

def evaluate_system(retriever, queries, qrels, evaluator, system_name, top_k=100):
    """Evaluate a retrieval system"""
    print(f"\n🔍 Evaluating {system_name}...")
    
    start_time = time.time()
    results = {}
    
    for i, query_info in enumerate(queries):
        query_id = query_info['query_id']
        query_text = query_info['query_text']
        
        # Retrieve documents
        if hasattr(retriever, 'batch_retrieve'):
            # Use batch retrieval if available
            batch_results = retriever.batch_retrieve([query_info], top_k)
            retrieved_docs = batch_results.get(query_id, [])
        else:
            # Use single query retrieval
            retrieved_docs = retriever.retrieve(query_text, top_k)
        
        results[query_id] = retrieved_docs
        
        # Print progress
        print_progress(i + 1, len(queries), f"{system_name} retrieval")
    
    # Evaluate results
    print(f"\n📊 Computing metrics for {system_name}...")
    evaluation = evaluator.evaluate_batch(results, qrels)
    
    end_time = time.time()
    evaluation['retrieval_time'] = end_time - start_time
    evaluation['queries_per_second'] = len(queries) / (end_time - start_time)
    
    return evaluation, results

def main():
    """Main evaluation function"""
    parser = argparse.ArgumentParser(description='Full Dataset IR Evaluation')
    parser.add_argument('--dataset', choices=['cranfield', 'vaswani', 'cacm', 'sample'], 
                       default='cranfield', help='Dataset to use')
    parser.add_argument('--top-k', type=int, default=100, help='Number of documents to retrieve')
    parser.add_argument('--rerank-k', type=int, default=10, help='Number of documents to rerank')
    parser.add_argument('--skip-dense', action='store_true', help='Skip dense retrieval (faster)')
    parser.add_argument('--skip-rerank', action='store_true', help='Skip LLM reranking (faster)')
    parser.add_argument('--sample-queries', type=int, help='Sample N queries for faster testing')
    
    args = parser.parse_args()
    
    print_separator(f"FULL DATASET EVALUATION - {args.dataset.upper()}")
    print(f"Dataset: {args.dataset}")
    print(f"Top-K Retrieval: {args.top_k}")
    print(f"Rerank Top-K: {args.rerank_k}")
    print(f"Skip Dense: {args.skip_dense}")
    print(f"Skip Rerank: {args.skip_rerank}")
    
    # Validate configuration
    try:
        Config.validate_config()
        print("✅ Configuration validated")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return
    
    # Initialize data processor
    print("\n📊 Loading dataset...")
    data_processor = DataProcessor()
    
    # Download/generate dataset
    if args.dataset == 'sample':
        success = data_processor.download_vaswani_dataset(use_real_data=False)
    else:
        success = data_processor.download_vaswani_dataset(use_real_data=True, dataset_name=args.dataset)
    
    if not success:
        print("❌ Failed to load dataset")
        return
    
    # Load dataset
    documents, queries, qrels = data_processor.load_dataset()
    
    # Sample queries if requested
    if args.sample_queries and args.sample_queries < len(queries):
        import random
        random.seed(42)
        queries = random.sample(queries, args.sample_queries)
        print(f"📝 Sampled {len(queries)} queries for testing")
    
    print(f"📈 Dataset loaded: {len(documents)} documents, {len(queries)} queries")
    
    # Build indices
    print("\n🏗️ Building indices...")
    
    # Sparse index
    print("Building sparse index...")
    sparse_indexer = SparseIndexer()
    start_time = time.time()
    sparse_indexer.build_index(documents)
    sparse_time = time.time() - start_time
    print(f"✅ Sparse index built in {sparse_time:.2f}s")
    print(f"   Vocabulary size: {sparse_indexer.get_vocabulary_size()}")
    
    # Dense index (optional)
    dense_indexer = None
    if not args.skip_dense:
        print("Building dense index...")
        dense_indexer = DenseIndexer()
        start_time = time.time()
        dense_indexer.build_index(documents)
        dense_time = time.time() - start_time
        print(f"✅ Dense index built in {dense_time:.2f}s")
        print(f"   Embedding dimension: {dense_indexer.embedding_dim}")
    
    # Initialize retrievers
    bm25_retriever = BM25Retriever()
    bm25_retriever.set_indexer(sparse_indexer)
    
    dense_retriever = None
    if dense_indexer:
        dense_retriever = DenseRetriever()
        dense_retriever.set_indexer(dense_indexer)
    
    # Initialize evaluator
    evaluator = IREvaluator()
    
    # Evaluation results
    all_evaluations = {}
    all_results = {}
    
    print_separator("SYSTEM EVALUATION")
    
    # Evaluate BM25
    bm25_eval, bm25_results = evaluate_system(
        bm25_retriever, queries, qrels, evaluator, "BM25", args.top_k
    )
    all_evaluations['BM25'] = bm25_eval
    all_results['BM25'] = bm25_results
    
    # Evaluate Dense Retrieval
    if dense_retriever:
        dense_eval, dense_results = evaluate_system(
            dense_retriever, queries, qrels, evaluator, "Dense", args.top_k
        )
        all_evaluations['Dense'] = dense_eval
        all_results['Dense'] = dense_results
    
    # Evaluate LLM Reranking
    if not args.skip_rerank:
        print(f"\n🤖 Evaluating LLM Reranking...")
        llm_reranker = LLMReranker()
        
        # Create document text mapping
        doc_texts = {doc['doc_id']: data_processor.get_document_text(doc) for doc in documents}
        
        start_time = time.time()
        reranked_results = {}
        
        for i, query_info in enumerate(queries):
            query_id = query_info['query_id']
            query_text = query_info['query_text']
            
            # Get BM25 results for reranking
            bm25_query_results = bm25_results.get(query_id, [])
            
            # Rerank top results
            reranked = llm_reranker.rerank(
                query_text, bm25_query_results, doc_texts, args.rerank_k
            )
            reranked_results[query_id] = reranked
            
            print_progress(i + 1, len(queries), "LLM reranking")
        
        # Evaluate reranked results
        print(f"\n📊 Computing metrics for LLM Reranking...")
        rerank_eval = evaluator.evaluate_batch(reranked_results, qrels)
        
        end_time = time.time()
        rerank_eval['retrieval_time'] = end_time - start_time
        rerank_eval['queries_per_second'] = len(queries) / (end_time - start_time)
        
        all_evaluations['Reranked'] = rerank_eval
        all_results['Reranked'] = reranked_results
    
    # Print results
    print_separator("EVALUATION RESULTS")
    
    print(f"\n📊 Performance Summary:")
    print(f"{'System':<12} {'MAP':<8} {'NDCG@10':<10} {'P@10':<8} {'Time(s)':<8} {'Q/s':<6}")
    print("-" * 60)
    
    for system_name, evaluation in all_evaluations.items():
        metrics = evaluation['average_metrics']
        map_score = metrics.get('MAP', 0)
        ndcg10 = metrics.get('NDCG@10', 0)
        p10 = metrics.get('P@10', 0)
        time_taken = evaluation.get('retrieval_time', 0)
        qps = evaluation.get('queries_per_second', 0)
        
        print(f"{system_name:<12} {map_score:<8.4f} {ndcg10:<10.4f} {p10:<8.4f} {time_taken:<8.1f} {qps:<6.1f}")
    
    # Detailed metrics
    print(f"\n📈 Detailed Metrics:")
    for system_name, evaluation in all_evaluations.items():
        print(f"\n{system_name} Results:")
        metrics = evaluation['average_metrics']
        for metric, value in metrics.items():
            print(f"  {metric}: {value:.4f}")
    
    # Save results
    results_file = f"results/full_evaluation_{args.dataset}_{len(documents)}docs_{len(queries)}queries.json"
    
    # Prepare results for JSON serialization
    json_results = {
        'dataset': args.dataset,
        'num_documents': len(documents),
        'num_queries': len(queries),
        'parameters': {
            'top_k': args.top_k,
            'rerank_k': args.rerank_k,
            'skip_dense': args.skip_dense,
            'skip_rerank': args.skip_rerank
        },
        'evaluations': {}
    }
    
    for system_name, evaluation in all_evaluations.items():
        json_results['evaluations'][system_name] = {
            'average_metrics': evaluation['average_metrics'],
            'num_queries': evaluation['num_queries'],
            'retrieval_time': evaluation.get('retrieval_time', 0),
            'queries_per_second': evaluation.get('queries_per_second', 0)
        }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(json_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    print_separator("EVALUATION COMPLETE")
    print("🎉 Full dataset evaluation completed successfully!")
    
    # Summary
    if 'BM25' in all_evaluations and 'Dense' in all_evaluations:
        bm25_map = all_evaluations['BM25']['average_metrics']['MAP']
        dense_map = all_evaluations['Dense']['average_metrics']['MAP']
        improvement = ((dense_map - bm25_map) / bm25_map * 100) if bm25_map > 0 else 0
        print(f"📈 Dense retrieval improved MAP by {improvement:+.1f}% over BM25")
    
    if 'Reranked' in all_evaluations and 'BM25' in all_evaluations:
        bm25_map = all_evaluations['BM25']['average_metrics']['MAP']
        rerank_map = all_evaluations['Reranked']['average_metrics']['MAP']
        improvement = ((rerank_map - bm25_map) / bm25_map * 100) if bm25_map > 0 else 0
        print(f"🤖 LLM reranking improved MAP by {improvement:+.1f}% over BM25")

if __name__ == "__main__":
    main()
