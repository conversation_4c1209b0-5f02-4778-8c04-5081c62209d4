"""
LLM Reranking evaluation script for sample queries
Takes BM25 top-20 results for first 20 queries and reranks them using LLM
"""
import os
import time
import json
from config import Config
from src.modules.data_processor import DataProcessor
from src.modules.sparse_indexer import SparseIndexer
from src.modules.bm25_retriever import BM25Retriever
from src.modules.llm_reranker import LLMReranker
from src.modules.evaluator import IREvaluator

def print_separator(title: str, char="=", width=80):
    """Print a formatted separator"""
    print(f"\n{char * width}")
    print(f" {title}")
    print(f"{char * width}")

def print_progress(current: int, total: int, prefix: str = "Progress"):
    """Print progress bar"""
    percent = (current / total) * 100
    bar_length = 50
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)
    print(f'\r{prefix}: |{bar}| {percent:.1f}% ({current}/{total})', end='', flush=True)
    if current == total:
        print()

def main():
    """Main LLM reranking evaluation function"""
    
    # Configuration
    NUM_QUERIES = 20  # Test with first 20 queries
    BM25_TOP_K = 20   # Get top 20 from BM25
    RERANK_TOP_K = 10 # Rerank to top 10
    K1 = 0.8          # Optimized BM25 parameters
    B = 0.5
    
    print_separator(f"LLM RERANKING EVALUATION - SAMPLE ({NUM_QUERIES} QUERIES)")
    print(f"Dataset: cranfield")
    print(f"Number of queries: {NUM_QUERIES}")
    print(f"BM25 Top-K (for reranking): {BM25_TOP_K}")
    print(f"Final Top-K (after reranking): {RERANK_TOP_K}")
    print(f"BM25 Parameters: k1={K1}, b={B} (optimized)")
    
    # Validate configuration
    try:
        Config.validate_config()
        print("✅ Configuration validated")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return
    
    # Load dataset
    print("\n📊 Loading dataset...")
    data_processor = DataProcessor()
    success = data_processor.download_vaswani_dataset(use_real_data=True, dataset_name='cranfield')
    
    if not success:
        print("❌ Failed to load dataset")
        return
    
    documents, all_queries, qrels = data_processor.load_dataset()
    
    # Take only first 20 queries
    queries = all_queries[:NUM_QUERIES]
    print(f"📈 Dataset loaded: {len(documents)} documents, {len(queries)} queries (sampled from {len(all_queries)})")
    
    # Build sparse index
    print("\n🏗️ Building sparse index...")
    sparse_indexer = SparseIndexer()
    start_time = time.time()
    sparse_indexer.build_index(documents)
    index_time = time.time() - start_time
    
    print(f"✅ Sparse index built in {index_time:.2f}s")
    print(f"   Vocabulary size: {sparse_indexer.get_vocabulary_size()}")
    
    # Initialize BM25 retriever with optimized parameters
    bm25_retriever = BM25Retriever(k1=K1, b=B)
    bm25_retriever.set_indexer(sparse_indexer)
    
    # Initialize LLM reranker
    print("\n🤖 Initializing LLM reranker...")
    llm_reranker = LLMReranker()
    
    # Create document text mapping for reranking
    print("📝 Preparing document texts...")
    doc_texts = {doc['doc_id']: data_processor.get_document_text(doc) for doc in documents}
    
    # Initialize evaluator
    evaluator = IREvaluator()
    
    print_separator("STEP 1: BM25 BASELINE RETRIEVAL")
    
    # Perform BM25 retrieval for sample queries
    print(f"🔍 Retrieving top-{BM25_TOP_K} documents with BM25...")
    bm25_start_time = time.time()
    
    bm25_results = {}
    for i, query_info in enumerate(queries):
        query_id = query_info['query_id']
        query_text = query_info['query_text']
        
        print(f"\n[{i+1}/{len(queries)}] Processing query: {query_id}")
        print(f"Query text: '{query_text}'")
        
        # Retrieve documents with BM25
        retrieved_docs = bm25_retriever.retrieve(query_text, BM25_TOP_K)
        bm25_results[query_id] = retrieved_docs
        
        print(f"Retrieved {len(retrieved_docs)} documents")
        
        # Show top 5 BM25 results
        print("Top 5 BM25 results:")
        for j, (doc_id, score) in enumerate(retrieved_docs[:5]):
            doc = next(d for d in documents if d['doc_id'] == doc_id)
            print(f"  {j+1}. {doc_id} (score: {score:.4f})")
            print(f"     Title: {doc['title']}")
    
    bm25_time = time.time() - bm25_start_time
    print(f"\n✅ BM25 retrieval completed in {bm25_time:.2f}s")
    
    # Evaluate BM25 baseline (on top-K results for fair comparison)
    print(f"\n📊 Evaluating BM25 baseline (top-{RERANK_TOP_K})...")
    bm25_eval_results = {}
    for query_id, results in bm25_results.items():
        bm25_eval_results[query_id] = results[:RERANK_TOP_K]
    
    bm25_evaluation = evaluator.evaluate_batch(bm25_eval_results, qrels)
    
    print(f"BM25 Baseline Performance (top-{RERANK_TOP_K}):")
    print(f"  MAP: {bm25_evaluation['average_metrics']['MAP']:.4f}")
    print(f"  NDCG@10: {bm25_evaluation['average_metrics']['NDCG@10']:.4f}")
    print(f"  P@10: {bm25_evaluation['average_metrics']['P@10']:.4f}")
    print(f"  MRR: {bm25_evaluation['average_metrics']['MRR']:.4f}")
    
    print_separator("STEP 2: LLM RERANKING")
    
    # Perform LLM reranking
    print(f"🤖 Reranking top-{BM25_TOP_K} results to top-{RERANK_TOP_K} with LLM...")
    rerank_start_time = time.time()
    
    reranked_results = {}
    failed_queries = []
    
    for i, query_info in enumerate(queries):
        query_id = query_info['query_id']
        query_text = query_info['query_text']
        
        print(f"\n[{i+1}/{len(queries)}] Reranking query: {query_id}")
        
        try:
            # Get BM25 results for this query
            bm25_query_results = bm25_results.get(query_id, [])
            
            if not bm25_query_results:
                print(f"⚠️  No BM25 results for query {query_id}")
                reranked_results[query_id] = []
                continue
            
            print(f"Reranking {len(bm25_query_results)} documents...")
            
            # Rerank with LLM
            reranked = llm_reranker.rerank(
                query_text, 
                bm25_query_results, 
                doc_texts, 
                RERANK_TOP_K
            )
            reranked_results[query_id] = reranked
            
            print(f"✅ Reranked to {len(reranked)} documents")
            
            # Show top 5 reranked results
            print("Top 5 reranked results:")
            for j, (doc_id, score) in enumerate(reranked[:5]):
                doc = next(d for d in documents if d['doc_id'] == doc_id)
                # Find original BM25 rank
                original_rank = next((k for k, (orig_doc_id, _) in enumerate(bm25_query_results) 
                                    if orig_doc_id == doc_id), -1) + 1
                print(f"  {j+1}. {doc_id} (LLM score: {score:.4f}, was BM25 rank: {original_rank})")
                print(f"     Title: {doc['title']}")
                
        except Exception as e:
            print(f"❌ Error reranking query {query_id}: {e}")
            failed_queries.append(query_id)
            # Fallback to BM25 results
            reranked_results[query_id] = bm25_results[query_id][:RERANK_TOP_K]
    
    rerank_time = time.time() - rerank_start_time
    print(f"\n✅ LLM reranking completed in {rerank_time:.2f}s")
    
    if failed_queries:
        print(f"⚠️  {len(failed_queries)} queries failed reranking, used BM25 fallback")
    
    print_separator("STEP 3: EVALUATION AND COMPARISON")
    
    # Evaluate reranked results
    print(f"📊 Evaluating reranked results...")
    reranked_evaluation = evaluator.evaluate_batch(reranked_results, qrels)
    
    # Print comparison results
    print(f"\n📈 Performance Comparison:")
    print(f"{'Metric':<12} {'BM25':<10} {'Reranked':<10} {'Improvement':<12}")
    print("-" * 50)
    
    bm25_metrics = bm25_evaluation['average_metrics']
    reranked_metrics = reranked_evaluation['average_metrics']
    
    key_metrics = ['MAP', 'NDCG@10', 'P@10', 'MRR', 'P@1', 'P@5']
    
    improvements = {}
    for metric in key_metrics:
        if metric in bm25_metrics and metric in reranked_metrics:
            bm25_val = bm25_metrics[metric]
            reranked_val = reranked_metrics[metric]
            
            if bm25_val > 0:
                improvement = ((reranked_val - bm25_val) / bm25_val) * 100
                improvements[metric] = improvement
                improvement_str = f"{improvement:+.1f}%"
            else:
                improvement_str = "N/A"
            
            print(f"{metric:<12} {bm25_val:<10.4f} {reranked_val:<10.4f} {improvement_str:<12}")
    
    # Query-level analysis
    print(f"\n🔍 Query-level Analysis:")
    
    query_improvements = []
    for query_result in reranked_evaluation['query_results']:
        query_id = query_result['query_id']
        reranked_ap = query_result['metrics']['AP']
        
        # Find corresponding BM25 result
        bm25_ap = 0
        for bm25_query_result in bm25_evaluation['query_results']:
            if bm25_query_result['query_id'] == query_id:
                bm25_ap = bm25_query_result['metrics']['AP']
                break
        
        if bm25_ap > 0:
            improvement = ((reranked_ap - bm25_ap) / bm25_ap) * 100
        else:
            improvement = 0 if reranked_ap == 0 else 100
        
        query_text = next(q['query_text'] for q in queries if q['query_id'] == query_id)
        query_improvements.append((query_id, improvement, bm25_ap, reranked_ap, query_text))
    
    # Sort by improvement
    query_improvements.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\nMost improved queries:")
    for i, (qid, improvement, bm25_ap, reranked_ap, query_text) in enumerate(query_improvements[:5]):
        print(f"  {i+1}. {qid} ({improvement:+.1f}%): {query_text}")
        print(f"     BM25 AP: {bm25_ap:.4f} → Reranked AP: {reranked_ap:.4f}")
    
    print(f"\nMost degraded queries:")
    for i, (qid, improvement, bm25_ap, reranked_ap, query_text) in enumerate(query_improvements[-3:]):
        print(f"  {i+1}. {qid} ({improvement:+.1f}%): {query_text}")
        print(f"     BM25 AP: {bm25_ap:.4f} → Reranked AP: {reranked_ap:.4f}")
    
    # Performance distribution
    positive_improvements = len([imp for _, imp, _, _, _ in query_improvements if imp > 0])
    negative_improvements = len([imp for _, imp, _, _, _ in query_improvements if imp < 0])
    no_change = len(query_improvements) - positive_improvements - negative_improvements
    
    print(f"\n📊 Improvement Distribution:")
    print(f"  Improved queries: {positive_improvements}/{len(queries)} ({positive_improvements/len(queries)*100:.1f}%)")
    print(f"  Degraded queries: {negative_improvements}/{len(queries)} ({negative_improvements/len(queries)*100:.1f}%)")
    print(f"  Unchanged queries: {no_change}/{len(queries)} ({no_change/len(queries)*100:.1f}%)")
    
    # Save results
    results_file = f"results/llm_reranking_sample_{NUM_QUERIES}queries.json"
    
    json_results = {
        'dataset': 'cranfield',
        'num_documents': len(documents),
        'num_queries': len(queries),
        'total_queries_in_dataset': len(all_queries),
        'parameters': {
            'bm25_top_k': BM25_TOP_K,
            'rerank_top_k': RERANK_TOP_K,
            'k1': K1,
            'b': B
        },
        'performance': {
            'bm25_time': bm25_time,
            'rerank_time': rerank_time,
            'total_time': bm25_time + rerank_time,
            'failed_queries': len(failed_queries)
        },
        'evaluations': {
            'bm25': {
                'average_metrics': bm25_evaluation['average_metrics'],
                'num_queries': bm25_evaluation['num_queries']
            },
            'reranked': {
                'average_metrics': reranked_evaluation['average_metrics'],
                'num_queries': reranked_evaluation['num_queries']
            }
        },
        'improvements': improvements,
        'query_level_analysis': {
            'positive_improvements': positive_improvements,
            'negative_improvements': negative_improvements,
            'no_change': no_change,
            'query_details': [
                {
                    'query_id': qid,
                    'query_text': qtext,
                    'improvement_pct': imp,
                    'bm25_ap': bm25_ap,
                    'reranked_ap': reranked_ap
                }
                for qid, imp, bm25_ap, reranked_ap, qtext in query_improvements
            ]
        }
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(json_results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    print_separator("LLM RERANKING EVALUATION COMPLETE")
    print("🎉 LLM reranking evaluation completed successfully!")
    print(f"📊 Processed {len(queries)} queries with {len(documents)} documents")
    
    # Summary
    overall_improvement = improvements.get('MAP', 0)
    if overall_improvement > 5:
        print(f"🟢 Excellent improvement: MAP +{overall_improvement:.1f}%")
    elif overall_improvement > 2:
        print(f"🟡 Good improvement: MAP +{overall_improvement:.1f}%")
    elif overall_improvement > 0:
        print(f"🟠 Modest improvement: MAP +{overall_improvement:.1f}%")
    else:
        print(f"🔴 No improvement: MAP {overall_improvement:+.1f}%")
    
    print(f"⏱️  Total processing time: {bm25_time + rerank_time:.1f}s")
    print(f"🤖 Average time per query: {rerank_time/len(queries):.1f}s")

if __name__ == "__main__":
    main()
